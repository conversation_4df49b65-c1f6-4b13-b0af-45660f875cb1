package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerBank;
import com.jri.biz.domain.request.CustomerBankForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerBankConvertImpl implements CustomerBankConvert {

    @Override
    public CustomerBank convert(CustomerBankForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerBank.CustomerBankBuilder customerBank = CustomerBank.builder();

        customerBank.bankId( form.getBankId() );
        customerBank.ciId( form.getCiId() );
        customerBank.bankBaseName( form.getBankBaseName() );
        customerBank.bankBaseAccount( form.getBankBaseAccount() );
        customerBank.internetbankFlag( form.getInternetbankFlag() );
        customerBank.debitCardFlag( form.getDebitCardFlag() );
        customerBank.receiptCardFlag( form.getReceiptCardFlag() );
        customerBank.receiptCardAccount( form.getReceiptCardAccount() );
        customerBank.receiptCardPassword( form.getReceiptCardPassword() );
        customerBank.receiptCardType( form.getReceiptCardType() );
        customerBank.commonBankName( form.getCommonBankName() );
        customerBank.commonBankAccount( form.getCommonBankAccount() );
        customerBank.commonInternetbankFlag( form.getCommonInternetbankFlag() );
        customerBank.commonReceiptCardFlag( form.getCommonReceiptCardFlag() );
        customerBank.commonInternetbankAccount( form.getCommonInternetbankAccount() );
        customerBank.commonReceiptCardPassword( form.getCommonReceiptCardPassword() );
        customerBank.commonInternetbankType( form.getCommonInternetbankType() );
        customerBank.cycle( form.getCycle() );
        customerBank.isDeleted( form.getIsDeleted() );

        return customerBank.build();
    }
}
