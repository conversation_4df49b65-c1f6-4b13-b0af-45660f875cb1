<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerInformationMapper">

    <resultMap id="CustomerInformationListVO" type="com.jri.biz.domain.vo.CustomerInformationListVO">
        <id property="customerId" column="customer_id" javaType="java.lang.Long"/>
        <result property="customerNo" column="customer_no" javaType="java.lang.String"/>
        <result property="customerName" column="customer_name" javaType="java.lang.String"/>
        <result property="contractMainId" column="contract_main_id" javaType="java.lang.Long"/>
        <result property="manger" column="manger" javaType="java.lang.String"/>
        <result property="branchOffice" column="branch_office" javaType="java.lang.String"/>
        <result property="address" column="address" javaType="java.lang.String"/>
        <result property="informationMark" column="information_mark" javaType="java.lang.String"/>
        <result property="customerStatus" column="customer_status" javaType="java.lang.String"/>
        <result property="customerProperty" column="customer_property" javaType="java.lang.String"/>
        <result property="nofeeReasonMark" column="nofee_reason_mark" javaType="java.lang.String"/>
        <result property="createTime" column="create_time" javaType="java.time.LocalDateTime"/>
        <result property="discard" column="discard" javaType="java.lang.Integer"/>
        <result property="discardReason" column="discard_reason" javaType="java.lang.String"/>
        <result property="completeness" column="completeness" javaType="java.math.BigDecimal"/>
    </resultMap>
    <resultMap id="CustomerInformationVO" type="com.jri.biz.domain.vo.CustomerInformationVO">
        <id property="customerId" column="customer_id"/>
        <result property="customerNo" column="customer_no"/>
        <result property="customerName" column="customer_name"/>
        <result property="contractMainId" column="contract_main_id"/>
        <result property="manger" column="manger"/>
        <result property="branchOffice" column="branch_office"/>
        <result property="address" column="address"/>
        <result property="informationMark" column="information_mark"/>
        <result property="customerStatus" column="customer_status"/>
        <result property="customerProperty" column="customer_property"/>
        <result property="nofeeReasonMark" column="nofee_reason_mark"/>
        <result property="discard" column="discard"/>
        <result property="discardReason" column="discard_reason"/>
        <result property="createBy" column="createBy"/>
        <result property="createTime" column="create_time"/>
        <result property="nofeeReason" column="nofee_reason"/>
        <result property="industry" column="industry"/>
        <result property="contactPerson" column="contactPerson"/>
        <result property="contactPhone" column="contactPhone"/>
        <result property="counselor" column="counselor"/>
        <result property="customerSuccess" column="customer_success"/>
        <result property="sponsorAccounting" column="sponsor_accounting"/>
        <result property="mangerUserId" column="manger_user_id"/>
        <result property="counselorUserId" column="counselor_user_id"/>
        <result property="customerSuccessUserId" column="customer_success_user_id"/>
        <result property="sponsorAccountingUserId" column="sponsor_accounting_user_id"/>
        <result property="walletAmount" column="wallet_amount"/>
        <result property="companyIdentification" column="company_identification"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <update id="changeManger">
        update customer_information set manger = #{form.manger}, manger_user_id = #{form.mangerUserId} where customer_id = #{form.customerId}
    </update>
    <update id="changeSponsorAccounting">
        update customer_information set sponsor_accounting = #{form.nameStr}, sponsor_accounting_user_id = #{form.userId} where customer_id = #{form.customerId}
    </update>
    <update id="changeCounselor">
        update customer_information set counselor = #{form.nameStr}, counselor_user_id = #{form.userId} where customer_id = #{form.customerId}
    </update>
    <update id="changeCustomerSuccess">
        update customer_information set customer_success = #{form.nameStr}, customer_success_user_id = #{form.userId} where customer_id = #{form.customerId}
    </update>


    <select id="listPage" parameterType="com.jri.biz.domain.request.CustomerInformationQuery"  resultType="com.jri.biz.domain.vo.CustomerInformationListVO">
        select
            info.customer_id,
            info.customer_no,
            info.customer_name,
            info.contract_main_id,
            info.manger,
            info.branch_office,
            info.address,
            info.information_mark,
            info.customer_status,
            info.customer_property,
            info.industry,
            info.nofee_reason_mark,
            info.discard,
            info.discard_reason,
            info.nofee_reason,
            info.counselor,
            info.customer_success,
            info.sponsor_accounting,
            info.completeness,
            info.company_identification,
            info.lng,
            info.manger_user_id,
            info.sponsor_accounting_user_id,
            info.counselor_user_id,
            info.customer_success_user_id,
            info.lat,
            info.create_time,
            bank.bank_id,
            bank.bank_base_name,
            bank.bank_base_account,
            bank.receipt_card_flag,
            bank.receipt_card_account,
            bank.receipt_card_password,
            bank.receipt_card_type,
            biz_info.credi_code,
            biz_info.type,
            biz_info.legal_person,
            biz_info.registered_address,
            biz_info.contract,
            biz_info.scope,
            biz_info.website,
            biz_info.bussiness_status,
            biz_info.registration_authority,
            biz_info.establish_date,
            biz_info.registered_capital,
            biz_info.industry business_industry,
            biz_info.registration_number,
            biz_info.open_date,
            biz_info.open_end,
            biz_info.organization_code,
            biz_info.approval_date,
            social_fund.social_account_flag,
            social_fund.social_account,
            social_fund.social_password,
            social_fund.fund_account_flag,
            social_fund.fund_account,
            social_fund.fund_password,
            tax.tax_registration_organ,
            tax.tax_organ_address,
            tax.rate_registration,
            tax.tax_real_name_flag,
            tax.individual_check_flag,
            tax.reserved_phone_number,
            tax.natural_person_password,
            tax.identification_method_flag,
            tax.certificate_account,
            tax.certificate_password,
            tax.drawing_sheet_flag,
            tax.invoice_flag,
            tax.invoice_seal_flag,
            tax.drawing_sheet_dept,
            tax.invoice_dept,
            tax.invoice_seal_dept,
            tax.invoice_type,
            tax.drawing_sheet_type,
            tax.invoice_limit,
            tax_rebate_identified.watch_time,
            tax_rebate_identified.approval_time,
            tax_rebate_identified.watch_instructions,
            tax_rebate_identified.dept,
            cc.name contactPerson,
            cc.phone contactPhone,
        <if test="query.lng != null and query.lat != null and query.distance != null">
            round(ST_Distance_Sphere(
            POINT(#{query.lng}, #{query.lat}),
            POINT(info.lng, info.lat)
            ) / 1000, 2) as distance,
        </if>
            manager_user.nick_name manager_user_name,
            sponsor_accounting_user.nick_name sponsor_accounting_user_name,
            counselor_user.nick_name counselor_user_name,
            customer_success_user.nick_name customer_success_user_name,
            a.date latestFollowUpDate
        from customer_information info
        left join customer_bank bank on info.customer_id = bank.ci_id and bank.is_deleted = 0
        left join customer_business_information biz_info on info.customer_id = biz_info.ci_id and biz_info.is_deleted = 0
        left join customer_social_fund social_fund on info.customer_id = social_fund.ci_id and social_fund.is_deleted = 0
        left join customer_tax_information tax on info.customer_id = tax.ci_id and tax.is_deleted = 0
        left join customer_tax_rebate_identified tax_rebate_identified on tax.id = tax_rebate_identified.main_id and tax_rebate_identified.is_deleted = 0
        left join customer_contact cc on info.contract_main_id = cc.id
        left join (
            select distinct ci_id ,max(date) as date from customer_follow
            group by ci_id
        ) a on a.ci_id = info.customer_id
        left join sys_user as manager_user on info.manger_user_id = manager_user.user_id
        left join sys_user as sponsor_accounting_user on info.sponsor_accounting_user_id = sponsor_accounting_user.user_id
        left join sys_user as counselor_user on info.counselor_user_id = counselor_user.user_id
        left join sys_user as customer_success_user on info.customer_success_user_id = customer_success_user.user_id
        left join (select min(operation_time) operation_time,
                          customer_id
                   from customer_user_relate_record
                   where role = 'sponsor_accounting' and pre_user_id is null
                   group by customer_id) as first_sponsor_accounting_relate_record on first_sponsor_accounting_relate_record.customer_id = info.customer_id
        left join (select min(operation_time) operation_time,
                            customer_id
                    from customer_user_relate_record
                    where role = 'customer_success' and pre_user_id is null
                    group by customer_id) as first_customer_success_relate_record on first_customer_success_relate_record.customer_id = info.customer_id
        <where>
            info.is_deleted = 0
            <if test="query.customerName != null and query.customerName != ''">
                and info.customer_name like concat('%',#{query.customerName},'%')
            </if>

            <if test="query.customerNo != null and query.customerNo != ''">
                and info.customer_no like concat('%',#{query.customerNo},'%')
            </if>
            <if test="query.industry != null and query.industry != ''">
                and info.industry = #{query.industry}
            </if>
            <if test="query.customerStatus != null and query.customerStatus != ''">
                and info.customer_status = #{query.customerStatus}
            </if>
            <if test="query.customerProperty != null and query.customerProperty != ''">
                and info.customer_property = #{query.customerProperty}
            </if>
            <if test="query.companyIdentification != null and query.companyIdentification != ''">
                and find_in_set(#{query.companyIdentification}, info.company_identification)
            </if>
            <if test="query.idList != null and query.idList.size > 0">
                AND info.customer_id in
                <foreach collection="query.idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <choose>
                <when test="query.discard != null and query.discard == 0">
                    and info.discard = 0 and info.risk_flag != true
                </when>
                <when test="query.discard != null and query.discard == 1">
                    and info.discard = 1
                </when>
                <when test="query.discard != null and query.discard == 99">
                    and info.risk_flag = true
                </when>
            </choose>
            <if test="query.keyword != null and query.keyword != ''">
                and (info.customer_name like concat('%',#{query.keyword},'%') or
                info.address like concat('%',#{query.keyword},'%') or
                info.customer_status like concat('%',#{query.keyword},'%') or
                info.customer_property like concat('%',#{query.keyword},'%') or
                cc.name like concat('%',#{query.keyword},'%') or
                cc.phone like concat('%',#{query.keyword},'%') or
                manager_user.nick_name like concat('%',#{query.keyword},'%') or
                sponsor_accounting_user.nick_name like concat('%',#{query.keyword},'%') or
                counselor_user.nick_name like concat('%',#{query.keyword},'%') or
                customer_success_user.nick_name like concat('%',#{query.keyword},'%')
                )
            </if>
            <if test="query.lng != null and query.lat != null and query.distance != null">
                and ST_Distance_Sphere(
                    POINT(#{query.lng}, #{query.lat}),
                    POINT(info.lng, info.lat)
                ) / 1000 &lt;= #{query.distance}
            </if>
            <if test="query.queryColumAndValue != null and query.queryColumAndValue.size > 0">
                and
                <foreach collection="query.queryColumAndValue" item="value" index="key" separator="and" open="(" close=")">
                    ${key} like concat('%',#{value},'%')
                </foreach>
            </if>
            <if test="query.sponsorAccountingAllocationStartDate != null and query.sponsorAccountingAllocationEndDate != null">
                and DATE_FORMAT(first_sponsor_accounting_relate_record.operation_time,'%Y-%m-%d') between #{query.sponsorAccountingAllocationStartDate} and #{query.sponsorAccountingAllocationEndDate}
            </if>
            <if test="query.customerSuccessAllocationStartDate != null and query.customerSuccessAllocationEndDate != null">
                and DATE_FORMAT(first_customer_success_relate_record.operation_time,'%Y-%m-%d') between #{query.customerSuccessAllocationStartDate} and #{query.customerSuccessAllocationEndDate}
            </if>
            <if test="query.establishDateStart != null and query.establishDateEnd != null">
                and (biz_info.establish_date between #{query.establishDateStart} and #{query.establishDateEnd} or
                biz_info.establish_date between REPLACE(#{query.establishDateStart}, '/', '-') and REPLACE(#{query.establishDateEnd}, '/', '-'))
            </if>
            ${query.dataScopeSql}
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <when test='query.orderByColumn == "no_limit"'>

            </when>
            <otherwise>
                <choose>
                    <when test="query.lng != null and query.lat != null and query.distance != null">
                        order by distance asc
                    </when>
                    <otherwise>
                        order by info.customer_no desc
                    </otherwise>
                </choose>
            </otherwise>
        </choose>

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerInformationVO">
        select ci.*, cc.name contactPerson, cc.phone contactPhone, su.nick_name createBy
        from customer_information ci
        left join customer_contact cc on ci.contract_main_id = cc.id
        left join sys_user su on ci.create_by = su.user_id
        where ci.customer_id = #{id}
        and ci.is_deleted = 0
    </select>
    <select id="selectMaxCode" resultType="String">
        select lpad(max(substring(customer_no, 6, 9)) + 1, 5, '0') AS max_number
        from customer_information
        where is_deleted = 0
    </select>
    <select id="getCountByCustomerStatus" resultType="java.lang.Long">
        select count(customer_id)
        from customer_information
        where is_deleted = 0 and discard = 0 and customer_status = #{customerStatus}
    </select>
    <select id="getCountByCustomerProperty" resultType="java.lang.Long">
        select count(customer_id)
        from customer_information
        where is_deleted = 0 and discard = 0 and customer_property = #{customerProperty}
    </select>
    <select id="getAssociatedCustomerInformationList"
            resultType="com.jri.biz.domain.vo.AssociatedCustomerInformationListVO">
        select * from customer_information
        <where>
            is_deleted = 0 and discard = 0
            <if test="query.customerName != null and query.customerName != ''">
                and customer_name like concat('%',#{query.customerName},'%')
            </if>

            <if test="query.customerNo != null and query.customerNo != ''">
                and customer_no like concat('%',#{query.customerNo},'%')
            </if>
            <if test="query.industry != null and query.industry != ''">
                and industry = #{query.industry}
            </if>
            <if test="query.customerStatus != null and query.customerStatus != ''">
                and customer_status = #{query.customerStatus}
            </if>
            <if test="query.customerProperty != null and query.customerProperty != ''">
                and customer_property = #{query.customerProperty}
            </if>
        </where>
        order by customer_id desc
    </select>

    <select id="getMonthLinkCustomer" resultType="java.lang.Long">
        select count(distinct customer_id)
        from customer_information information
        inner join sys_user ON (
        <choose>
            <when test='role == "customer_success"'>
                information.customer_success_user_id = user_id
            </when>
            <when test='role == "counselor"'>
                information.counselor_user_id = user_id
            </when>
            <when test='role == "manager"'>
                information.manger_user_id = user_id
            </when>
            <when test='role == "sponsor_accounting"'>
                information.sponsor_accounting_user_id = user_id
            </when>
            <otherwise>
                information.manger_user_id = user_id
                OR information.counselor_user_id = user_id
                OR information.customer_success_user_id = user_id
                OR information.sponsor_accounting_user_id = user_id
            </otherwise>
        </choose>
        ) and del_flag = '0'
        left join sys_dept on sys_user.dept_id = sys_dept.dept_id
        where is_deleted = 0
        and discard = 0 and is_deleted = 0 and (customer_property != '个体户' or customer_property is null) and (customer_status != '个体户开票' or customer_status is null)
        <if test="customerSuccessUserId != null">
            and
            <choose>
                <when test='role == "customer_success"'>
                    (customer_success_user_id = #{customerSuccessUserId})
                </when>
                <when test='role == "counselor"'>
                    (counselor_user_id = #{customerSuccessUserId})
                </when>
                <when test='role == "manager"'>
                    (manger_user_id = #{customerSuccessUserId})
                </when>
                <when test='role == "sponsor_accounting"'>
                    (sponsor_accounting_user_id = #{customerSuccessUserId})
                </when>
                <otherwise>
                    (
                    (manger_user_id = #{customerSuccessUserId})
                    OR (counselor_user_id = #{customerSuccessUserId})
                    OR (customer_success_user_id = #{customerSuccessUserId})
                    OR (sponsor_accounting_user_id = #{customerSuccessUserId})
                    )
                </otherwise>
            </choose>
        </if>
        <if test="customerSuccess != null and customerSuccess != ''">
            and sys_user.nick_name like concat('%',#{customerSuccess}, '%')
        </if>
        <if test="deptId != null">
            and (FIND_IN_SET(#{deptId},sys_dept.ancestors)>0 or
            #{deptId}=sys_dept.dept_id)
        </if>
    </select>

    <select id="getCountByIdentification" resultType="java.lang.Long">
        select count(customer_id)
        from customer_information
        where is_deleted = 0 and discard = 0 and find_in_set(#{type}, company_identification)
    </select>

    <select id="getContactImportTemplate" resultType="com.jri.biz.domain.vo.CustomerContactImportTemplate">
        select customer_name,
               customer_no
        from customer_information
        where is_deleted = 0
        order by customer_no desc
    </select>

    <select id="getCustomerByCustomerName" resultType="com.jri.biz.domain.entity.CustomerInformation">
        select
            *
        from customer_information
        where customer_name = #{customerName} and is_deleted = 0
    </select>

    <select id="userCustomerListPage"
            resultType="com.jri.biz.domain.vo.customerUser.UserCustomerInformationListVO">
        select
            info.customer_id,
            info.customer_name,
            info.customer_no,
            contact.name contact_person,
            contact.phone contact_phone,
            info.address
        from user_customer_bind_record bind_record
        left join customer_information info on info.customer_id = bind_record.customer_id
        left join customer_contact contact on info.contract_main_id = contact.id
        <where>
            info.is_deleted = 0 and (bind_record.user_id = #{query.userId} and bind_record.bind_status = 'bind')
            <if test="query.customerName != null and query.customerName != ''">
                and info.customer_name like concat('%',#{query.customerName},'%')
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and info.customer_name like concat('%',#{query.keyword},'%')
            </if>
        </where>
    </select>

    <select id="getCounselorUserByCustomerUserId" resultType="com.jri.common.core.domain.entity.SysUser">
        select
            user.nick_name,
            user.phonenumber
        from user_customer_bind_record bind_record
        left join customer_information info on bind_record.customer_id = info.customer_id
        left join sys_user user on info.manger_user_id = user.user_id
        where bind_record.user_id = #{userId} and bind_record.bind_status = 'bind' and user.status = '0' and user.del_flag = '0'
        limit 1
    </select>

    <delete id="delete" parameterType="Long">
        update customer_information set is_deleted ='1' where id=#{id}
    </delete>
</mapper>
