package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.OrderRecord;
import com.jri.biz.domain.request.OrderRecordForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class OrderRecordConvertImpl implements OrderRecordConvert {

    @Override
    public OrderRecord convert(OrderRecordForm form) {
        if ( form == null ) {
            return null;
        }

        OrderRecord.OrderRecordBuilder orderRecord = OrderRecord.builder();

        orderRecord.id( form.getId() );
        orderRecord.createBy( form.getCreateBy() );
        orderRecord.recordName( form.getRecordName() );
        orderRecord.executor( form.getExecutor() );
        orderRecord.orderId( form.getOrderId() );
        orderRecord.remark( form.getRemark() );

        return orderRecord.build();
    }
}
