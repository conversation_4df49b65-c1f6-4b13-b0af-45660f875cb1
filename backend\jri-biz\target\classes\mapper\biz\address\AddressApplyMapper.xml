<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.address.AddressApplyMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.address.AddressApplyListVO">
        select *
        from (
            select
            apply.id,
            apply.create_by,
            apply.create_time,
            apply.update_by,
            apply.update_time,
            apply.customer_id,
            apply.property_id,
            apply.payment_id,
            apply.area_id,
            property.name property_name,
            information.customer_name,
            if(isnull(apply.property_id),
            if(exists(select * from address_property_ownership as property2
            where property2.area_id = apply.area_id
            and property2.use_status = 'idle'
            and property2.enable = '1'
            and property2.is_deleted = 0
            and property2.lease_start_date &lt;= DATE_FORMAT(payment.payment_start_time ,'%Y-%m-01')
            and property2.lease_end_date >= LAST_DAY(payment.payment_end_time)),
            '待选地址', '暂无地址'),
            apply.status
            ) as status,
            area.name as area_name,
            payment.payment_start_time,
            payment.payment_end_time,
            payment.payment_no
            from address_apply apply
            left join address_property_ownership as property on apply.property_id = property.id
            left join customer_information information on apply.customer_id = information.customer_id
            left join finance_payment payment on apply.payment_id = payment.id
            left join basic_administrative area on apply.area_id = area.id
            left join sys_user manager_user ON information.manger_user_id = manager_user.user_id
            left join sys_user counselor_user ON information.counselor_user_id = counselor_user.user_id
            left join sys_user customer_success_user ON information.customer_success_user_id = customer_success_user.user_id
            left join sys_user sponsor_accounting_user ON information.sponsor_accounting_user_id = sponsor_accounting_user.user_id
        <where>
            apply.is_deleted = 0
            <if test="query.customerName != null and query.customerName != ''">
                and information.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.areaId != null">
                and apply.area_id = #{query.areaId}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and apply.create_by like concat('%', #{query.createBy}, '%')
            </if>

            <if test="query.paymentEndTime != null">
                and payment.payment_end_time &lt;= LAST_DAY(#{query.paymentEndTime} + '-01')
            </if>
            ${query.dataScopeSql}
        </where>
        order by apply.create_time desc
        ) as result
        <if test="query.status != null and query.status != ''">
            where `status` = #{query.status}
        </if>
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.address.AddressApplyVO">
        select
            apply.id, apply.create_by, apply.create_time, apply.update_by, apply.update_time, apply.is_deleted,
            apply.customer_id, apply.property_id, apply.payment_id, apply.address, apply.address_cost,
            apply.gross_profit, apply.reason, apply.review_time, apply.review_person,
            apply.remark, apply.area_id, apply.hosting_type,
            if(isnull(apply.property_id),
               if(exists(select * from address_property_ownership as property2
                         where property2.area_id = apply.area_id
                           and property2.use_status = 'idle'
                           and property2.enable = '1'
                           and property2.is_deleted = 0
                           and property2.lease_end_date >= payment.payment_end_time),
                  '待选地址', '暂无地址'),
               apply.status
            ) as status,
            area.ancestors as area_ancestors,
            property.name property_name,
            information.customer_name,
            payment.payment_start_time,
            payment.payment_end_time,
            payment.payment_amount,
            payment.payment_no
        from address_apply apply
        left join address_property_ownership as property on apply.property_id = property.id
        left join customer_information information on apply.customer_id = information.customer_id
        left join finance_payment payment on apply.payment_id = payment.id
        left join basic_administrative area on property.area_id = area.id
        where apply.id = #{id}
    </select>

    <select id="getExpireApply" resultType="com.jri.biz.domain.entity.address.AddressApply">
        select
            apply.*
        from address_apply apply
        left join finance_payment payment on apply.payment_id = payment.id
        where LAST_DAY(payment.payment_end_time) &lt; now() and apply.status = '已完成'
    </select>

</mapper>
