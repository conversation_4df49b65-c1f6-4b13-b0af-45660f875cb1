package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerDiscardRecord;
import com.jri.biz.domain.request.CustomerDiscardRecordForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerDiscardRecordConvertImpl implements CustomerDiscardRecordConvert {

    @Override
    public CustomerDiscardRecord convert(CustomerDiscardRecordForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerDiscardRecord.CustomerDiscardRecordBuilder customerDiscardRecord = CustomerDiscardRecord.builder();

        customerDiscardRecord.id( form.getId() );
        customerDiscardRecord.ciId( form.getCiId() );
        customerDiscardRecord.discard( form.getDiscard() );
        customerDiscardRecord.discardReason( form.getDiscardReason() );

        return customerDiscardRecord.build();
    }
}
