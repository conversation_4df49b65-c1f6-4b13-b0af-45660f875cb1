<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.BorrowRecordMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.BorrowRecordListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.BorrowRecordVO">
        select br.*, cc.contract_type, cc.contract_name, cc.contract_no, ci.customer_no, ci.customer_name, su.nick_name
        from borrow_record br
                 left join customer_contract cc on br.main_id = cc.contract_id
                 left join customer_information ci on cc.ci_id = ci.customer_id
                 left join sys_user su on br.user_id = su.user_id
        where br.id = #{id}
    </select>
    <select id="listMyCreate" resultType="com.jri.biz.domain.vo.BorrowRecordListVO">
        select br.id,
               cc.contract_name,
               cc.contract_no,
               ci.customer_no,
               ci.customer_name,
               su.nick_name,
               br.create_time,
               br.user_id,
               br.expiration_time,
               br.borrow_reason,
               br.status,
               cc.ci_id
        from borrow_record br
                 left join customer_contract cc on br.main_id = cc.contract_id
                 left join customer_information ci on cc.ci_id = ci.customer_id
                 left join sys_user su on br.user_id = su.user_id
        <where>
            br.is_deleted = 0
            and br.create_by = #{query.userId}
            <if test="query.contractName != null and query.contractName != ''">
                and cc.contract_name like concat('%', #{query.contractName}, '%')
            </if>
            <if test="query.contractNo != null and query.contractNo != ''">
                and cc.contract_no like concat('%', #{query.contractNo}, '%')
            </if>
            <if test="query.customerNo != null and query.customerNo != ''">
                and ci.customer_no like concat('%', #{query.customerNo}, '%')
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                and br.status = #{query.status}
            </if>
        </where>
        order by br.create_time desc
    </select>
    <select id="listMyAudit" resultType="com.jri.biz.domain.vo.BorrowRecordListVO">
        select br.id,
        cc.contract_name,
        cc.contract_no,
        ci.customer_no,
        ci.customer_name,
        su.nick_name,
        br.create_time,
        br.user_id,
        br.expiration_time,
        br.borrow_reason,
        bnh.review_status status,
        cc.ci_id
        from borrow_record br
        left join customer_contract cc on br.main_id = cc.contract_id
        left join customer_information ci on cc.ci_id = ci.customer_id
        left join sys_user su on br.user_id = su.user_id
        left join (
        select max(bnh.node_key) node_key,br.id from borrow_record br left join biz_node_history bnh ON
        bnh.main_id = br.id
        and bnh.type = '1'
        where bnh.user_id = #{query.userId}
        group by br.id
        )mm on br.id = mm.id
        left join biz_node_history bnh on mm.id = bnh.main_id and mm.node_key = bnh.node_key
        <where>
            br.is_deleted = 0
            and bnh.user_id = #{query.userId}
            <if test="query.contractName != null and query.contractName != ''">
                and cc.contract_name like concat('%', #{query.contractName}, '%')
            </if>
            <if test="query.contractNo != null and query.contractNo != ''">
                and cc.contract_no like concat('%', #{query.contractNo}, '%')
            </if>
            <if test="query.customerNo != null and query.customerNo != ''">
                and ci.customer_no like concat('%', #{query.customerNo}, '%')
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                and bnh.review_status = #{query.status}
            </if>
        </where>
        order by br.create_time desc
    </select>
</mapper>
