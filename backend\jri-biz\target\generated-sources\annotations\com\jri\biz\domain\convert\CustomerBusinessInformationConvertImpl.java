package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerBusinessInformation;
import com.jri.biz.domain.request.CustomerBusinessInformationForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerBusinessInformationConvertImpl implements CustomerBusinessInformationConvert {

    @Override
    public CustomerBusinessInformation convert(CustomerBusinessInformationForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerBusinessInformation.CustomerBusinessInformationBuilder customerBusinessInformation = CustomerBusinessInformation.builder();

        customerBusinessInformation.businessInformationId( form.getBusinessInformationId() );
        customerBusinessInformation.ciId( form.getCiId() );
        customerBusinessInformation.type( form.getType() );
        customerBusinessInformation.crediCode( form.getCrediCode() );
        customerBusinessInformation.legalPerson( form.getLegalPerson() );
        customerBusinessInformation.registeredAddress( form.getRegisteredAddress() );
        customerBusinessInformation.contract( form.getContract() );
        customerBusinessInformation.scope( form.getScope() );
        customerBusinessInformation.website( form.getWebsite() );
        customerBusinessInformation.bussinessStatus( form.getBussinessStatus() );
        customerBusinessInformation.registrationAuthority( form.getRegistrationAuthority() );
        customerBusinessInformation.establishDate( form.getEstablishDate() );
        customerBusinessInformation.registeredCapital( form.getRegisteredCapital() );
        customerBusinessInformation.industry( form.getIndustry() );
        customerBusinessInformation.registrationNumber( form.getRegistrationNumber() );
        customerBusinessInformation.openDate( form.getOpenDate() );
        customerBusinessInformation.openEnd( form.getOpenEnd() );
        customerBusinessInformation.organizationCode( form.getOrganizationCode() );
        customerBusinessInformation.approvalDate( form.getApprovalDate() );
        customerBusinessInformation.nationalTaxAccount( form.getNationalTaxAccount() );
        customerBusinessInformation.nationalTaxPassward( form.getNationalTaxPassward() );
        customerBusinessInformation.individualTaxAccount( form.getIndividualTaxAccount() );
        customerBusinessInformation.individualTaxPassword( form.getIndividualTaxPassword() );
        customerBusinessInformation.isDeleted( form.getIsDeleted() );
        customerBusinessInformation.isIndividual( form.getIsIndividual() );

        return customerBusinessInformation.build();
    }
}
