package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicTaxBureau;
import com.jri.biz.domain.request.BasicTaxBureauForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BasicTaxBureauConvertImpl implements BasicTaxBureauConvert {

    @Override
    public BasicTaxBureau convert(BasicTaxBureauForm form) {
        if ( form == null ) {
            return null;
        }

        BasicTaxBureau.BasicTaxBureauBuilder basicTaxBureau = BasicTaxBureau.builder();

        basicTaxBureau.id( form.getId() );
        basicTaxBureau.parentId( form.getParentId() );
        basicTaxBureau.ancestors( form.getAncestors() );
        basicTaxBureau.name( form.getName() );
        basicTaxBureau.sort( form.getSort() );
        basicTaxBureau.remark( form.getRemark() );
        basicTaxBureau.enable( form.getEnable() );
        basicTaxBureau.address( form.getAddress() );
        basicTaxBureau.lat( form.getLat() );
        basicTaxBureau.lng( form.getLng() );

        return basicTaxBureau.build();
    }
}
