<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.BasicBookkeepingStopMapper">
    <update id="updateStatusNormal">
        update basic_bookkeeping_stop set enable = '1' where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="listPage" resultType="com.jri.biz.domain.vo.BasicBookkeepingStopListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.BasicBookkeepingStopVO">
        select *
        from basic_bookkeeping_stop
        where id = #{id}
    </select>
    <select id="selectChildrenById" resultType="com.jri.biz.domain.entity.BasicBookkeepingStop">
        select *
        from basic_bookkeeping_stop
        where is_deleted = 0 and find_in_set(#{id}, ancestors)
    </select>
    <select id="getList" resultType="com.jri.biz.domain.vo.BasicBookkeepingStopListVO">
        select *
        from basic_bookkeeping_stop
        <where>
            is_deleted = 0
            <if test="query.name != null and query.name != ''">
                and `name` like concat('%', #{query.name}, '%')
            </if>
            <if test="query.enable != null and query.enable != ''">
                and enable = #{query.enable}
            </if>
        </where>
        order by parent_id, sort
    </select>
    <select id="selectNormalChildrenById" resultType="java.lang.Integer">
        select count(*) from basic_bookkeeping_stop where enable = '1' and is_deleted = 0 and find_in_set(#{id}, ancestors)
    </select>
</mapper>
