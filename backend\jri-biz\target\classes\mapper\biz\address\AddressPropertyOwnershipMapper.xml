<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.address.AddressPropertyOwnershipMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.address.AddressPropertyOwnershipListVO">
        select
            property.id,
            property.name,
            property.lessor_id,
            property.lease_start_date,
            property.lease_end_date,
            property.lease_price,
            property.remark,
            property.enable,
            property.use_status,
            lessor.lessor_name,
            lessor.phone
        from address_property_ownership as property
        left join address_lessor as lessor on property.lessor_id = lessor.id
        <where>
            property.is_deleted = 0
            <if test="query.name != null and query.name != ''">
                and property.name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.lessorName != null and query.lessorName != ''">
                and lessor.lessor_name like concat('%', #{query.lessorName}, '%')
            </if>
            <if test="query.useStatus != null and query.useStatus != ''">
                and property.use_status = #{query.useStatus}
            </if>
        </where>
        order by property.create_time desc
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.address.AddressPropertyOwnershipVO">
        select
            property.*,
            area.ancestors as area_ancestors,
            lessor.lessor_name,
            lessor.phone
        from address_property_ownership as property
        left join address_lessor as lessor on property.lessor_id = lessor.id
        left join basic_administrative area on property.area_id = area.id
        where property.id = #{id}
    </select>
</mapper>
