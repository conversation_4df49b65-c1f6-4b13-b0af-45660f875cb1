package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.UserCustomerBindRecord;
import com.jri.biz.domain.request.UserCustomerBindRecordForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class UserCustomerBindRecordConvertImpl implements UserCustomerBindRecordConvert {

    @Override
    public UserCustomerBindRecord convert(UserCustomerBindRecordForm form) {
        if ( form == null ) {
            return null;
        }

        UserCustomerBindRecord.UserCustomerBindRecordBuilder userCustomerBindRecord = UserCustomerBindRecord.builder();

        userCustomerBindRecord.userId( form.getUserId() );

        return userCustomerBindRecord.build();
    }
}
