<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusCustomerOrClueMapper">

    <resultMap id="CusClueInSeaListVO" type="com.jri.biz.cus.domain.vo.CusClueInSeaListVO"
               autoMapping="true">
        <id property="id" column="id"/>
        <result property="source" column="source"/>
        <result property="sourceId" column="source_id"/>
        <result property="sourceName" column="source_name"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="lastFollowTime" column="last_follow_time"/>
        <result property="followStatus" column="follow_status"/>
        <result property="seaId" column="sea_id"/>
        <result property="seaName" column="seaName"/>
        <result property="rule" column="rule"/>
        <result property="manageId" column="manage_id"/>
        <result property="currentUserId" column="current_user_id"/>
        <result property="currentUserName" column="currentUserName"/>
        <result property="contactName" column="contact_name"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="lastModifiedTime" column="last_modified_time"/>
        <result property="protectionStartTime" column="protection_start_time"/>
        <result property="appealFlag" column="appeal_flag"/>
        <result property="deptIds" column="dept_ids"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <resultMap id="CusCustomerInSeaListVO" type="com.jri.biz.cus.domain.vo.CusCustomerInSeaListVO"
               autoMapping="true">
        <id property="id" column="id"/>
        <result property="companyName" column="company_name"/>
        <result property="source" column="source"/>
        <result property="sourceId" column="source_id"/>
        <result property="sourceName" column="source_name"/>
        <result property="level" column="level"/>
        <result property="becomeTime" column="become_time"/>
        <result property="remark" column="remark"/>
        <result property="taxNature" column="tax_nature"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="lastFollowTime" column="last_follow_time"/>
        <result property="followStatus" column="follow_status"/>
        <result property="seaId" column="sea_id"/>
        <result property="seaName" column="seaName"/>
        <result property="rule" column="rule"/>
        <result property="manageId" column="manage_id"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="lastModifiedTime" column="last_modified_time"/>
        <result property="protectionStartTime" column="protection_start_time"/>
        <result property="deptIds" column="dept_ids"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <resultMap id="CusCustomerOrClueVO" type="com.jri.biz.cus.domain.vo.CusCustomerOrClueVO"
               autoMapping="true">
        <id property="id" column="id"/>
        <result property="source" column="source"/>
        <result property="sourceId" column="source_id"/>
        <result property="sourceName" column="source_name"/>
        <result property="introductionCustomerId" column="introduction_customer_id"/>
        <result property="introductionCustomerName" column="introduction_customer_name"/>
        <result property="companyName" column="company_name"/>
        <result property="remark" column="remark"/>
        <result property="industry" column="industry"/>
        <result property="area" column="area"/>
        <result property="address" column="address"/>
        <result property="taxNature" column="tax_nature"/>
        <result property="firstFollowTime" column="first_follow_time"/>
        <result property="level" column="level"/>
        <result property="seaId" column="sea_id"/>
        <result property="seaName" column="seaName"/>
        <result property="manageId" column="manage_id"/>
        <result property="isSea" column="is_sea"/>
        <result property="mainContactId" column="main_contact_id"/>
        <result property="type" column="type"/>
        <result property="entryType" column="entry_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="lastFollowTime" column="last_follow_time"/>
        <result property="getTime" column="get_time"/>
        <result property="lastFollowUser" column="last_follow_user"/>
        <result property="followStatus" column="follow_status"/>
        <result property="currentUserId" column="current_user_id"/>
        <result property="currentUserName" column="currentUserName"/>
        <result property="phone" column="phone"/>
        <result property="becomeTime" column="become_time"/>
        <result property="duration" column="duration"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="productName"/>
        <result property="contactName" column="contact_name"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="post" column="post"/>
        <result property="wx" column="wx"/>
        <result property="qq" column="qq"/>
        <result property="sex" column="sex"/>
        <result property="email" column="email"/>
        <result property="birthday" column="birthday"/>
        <result property="lastModifiedTime" column="last_modified_time"/>
        <result property="protectionStartTime" column="protection_start_time"/>
        <result property="deptIds" column="dept_ids"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="sourceBiz" column="source_biz"/>
        <result property="sourceAppealStatus" column="source_appeal_status"/>
    </resultMap>

    <update id="updateDuration">
        update cus_customer_or_clue set duration = #{duration}
        where id = #{id}
    </update>


    <select id="myClueList" resultType="com.jri.biz.cus.domain.vo.CusCustomerOrClueListVO">
        select ccoc.*,
        ccc.contact_name,
        ccc.contact_phone,
        ccc.post,
        ccc.wx,
        ccc.qq,
        ccc.sex,
        ccc.email,
        ccc.birthday,
        coalesce(product.product_name, type.type_name) as product_name,
        source.name source_name
        from cus_customer_or_clue ccoc
        left join cus_cc_contact ccc on ccoc.main_contact_id = ccc.id
        left join cus_source source on ccoc.source_id = source.id
        left join business_product product on ccoc.product_id = product.id
        left join business_type type on ccoc.product_id = type.id
        <where>
            ccoc.is_deleted = 0
            and ccoc.type = '0'
            and ccoc.is_sea = '0'
            and ccoc.current_user_id = #{query.userId}
            <if test="query.createBy != null and query.createBy != ''">
                and ccoc.create_by like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.source != null and query.source != ''">
                and ccoc.source = #{query.source}
            </if>
            <if test="query.sourceId != null">
                and ccoc.source_id = #{query.sourceId}
            </if>
            <if test="query.followStatus != null and query.followStatus != ''">
                <choose>
                    <when test='query.followStatus == "3"'>
                        and appeal_flag = 1
                    </when>
                    <otherwise>
                        and ccoc.follow_status = #{query.followStatus} and appeal_flag = 0
                    </otherwise>
                </choose>
            </if>
            <if test="query.id != null">
                and ccoc.id = #{query.id}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and (ccc.contact_name like concat('%',#{query.keyword},'%') or
                ccc.contact_phone like concat('%',#{query.keyword},'%') or
                source.name like concat('%',#{query.keyword},'%') or
                product.product_name like concat('%',#{query.keyword},'%') or
                type.type_name like concat('%',#{query.keyword},'%')
                )
            </if>
            <if test="query.contactName != null and query.contactName != ''">
                and ccc.contact_name like concat('%', #{query.contactName}, '%')
            </if>
            <if test="query.contactPhone != null and query.contactPhone != ''">
                and ccc.contact_phone like concat('%', #{query.contactPhone}, '%')
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and ccoc.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.lastFollowTimeStart != null and query.lastFollowTimeEnd != null">
                and ccoc.last_follow_time between #{query.lastFollowTimeStart} and #{query.lastFollowTimeEnd}
            </if>
            <if test="query.idList != null and query.idList.size() > 0">
                <foreach collection="query.idList" item="id" open="and ccoc.id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="query.remark != null and query.remark != ''">
                and ccoc.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.companyName != null and query.companyName !=''">
                and ccoc.company_name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.productId != null">
                and ccoc.product_id = #{query.productId}
            </if>
            <if test="query.taxNature != null and query.taxNature != ''">
                and ccoc.tax_nature = #{query.taxNature}
            </if>
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by ccoc.create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="shareClueList" resultType="com.jri.biz.cus.domain.vo.CusCustomerOrClueListVO">
        select ccoc.*,
        ccc.contact_name,
        ccc.contact_phone,
        ccc.post,
        ccc.wx,
        ccc.qq,
        ccc.sex,
        ccc.email,
        ccc.birthday,
        su.nick_name currentUserName,
        source.name source_name
        from cus_customer_or_clue ccoc
        left join cus_cc_contact ccc on ccoc.main_contact_id = ccc.id
        left join cus_cc_share ccs on ccoc.id = ccs.cc_id
        left join cus_source source on ccoc.source_id = source.id
        left join sys_user su on ccoc.current_user_id = su.user_id
        <where>
            ccoc.is_deleted = 0
            and ccoc.type = '0'
            and ccoc.is_sea = '0'
            and ccs.user_id = #{query.userId}
            <if test="query.createBy != null and query.createBy != ''">
                and ccoc.create_by like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.source != null and query.source != ''">
                and ccoc.source = #{query.source}
            </if>
            <if test="query.sourceId != null">
                and ccoc.source_id = #{query.sourceId}
            </if>
            <if test="query.followStatus != null and query.followStatus != ''">
                <choose>
                    <when test='query.followStatus == "3"'>
                        and appeal_flag = 1
                    </when>
                    <otherwise>
                        and ccoc.follow_status = #{query.followStatus} and appeal_flag = 0
                    </otherwise>
                </choose>
            </if>
            <if test="query.currentUserName != null and query.currentUserName != ''">
                and su.nick_name like concat('%',#{query.currentUserName},'%')
            </if>
            <if test="query.contactName != null and query.contactName != ''">
                and ccc.contact_name like concat('%', #{query.contactName}, '%')
            </if>
            <if test="query.contactPhone != null and query.contactPhone != ''">
                and ccc.contact_phone like concat('%', #{query.contactPhone}, '%')
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and ccoc.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.lastFollowTimeStart != null and query.lastFollowTimeEnd != null">
                and ccoc.last_follow_time between #{query.lastFollowTimeStart} and #{query.lastFollowTimeEnd}
            </if>
            <if test="query.idList != null and query.idList.size() > 0">
                <foreach collection="query.idList" item="id" open="and ccoc.id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="query.remark != null and query.remark != ''">
                and ccoc.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.companyName != null and query.companyName !=''">
                and ccoc.company_name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.productId != null">
                and ccoc.product_id = #{query.productId}
            </if>
            <if test="query.taxNature != null and query.taxNature != ''">
                and ccoc.tax_nature = #{query.taxNature}
            </if>
        </where>
        group by ccoc.id
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by ccoc.create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="getDetailById" resultMap="CusCustomerOrClueVO">
        select ccoc.*,
               cs.name      seaName,
               cs.manage_id,
               cs.dept_ids,
               ccc.contact_name,
               ccc.contact_phone,
               ccc.post,
               ccc.wx,
               ccc.qq,
               ccc.sex,
               ccc.email,
               ccc.birthday,
               su.nick_name currentUserName,
               source.name as source_name,
               source.biz as source_biz,
               appeal_record.status as source_appeal_status,
               info.customer_name introduction_customer_name
        from cus_customer_or_clue ccoc
        left join cus_sea cs on ccoc.sea_id = cs.id
        left join cus_cc_contact ccc on ccoc.main_contact_id = ccc.id
        left join sys_user su on ccoc.current_user_id = su.user_id
        left join cus_source source on ccoc.source_id = source.id
        left join customer_information info on ccoc.introduction_customer_id = info.customer_id
        left join clue_appeal_record appeal_record on ccoc.id = appeal_record.clue_id
        where ccoc.id = #{id}
    </select>
    <select id="getUserIds" resultType="java.lang.Long">
        select ccs.user_id
        from cus_cc_share ccs
                 left join sys_user su on ccs.user_id = su.user_id
        where ccs.cc_id = #{ccId}
          and su.del_flag = '0'
    </select>
    <select id="clueInSeaList" resultMap="CusClueInSeaListVO">
        select ccoc.*,
        ccc.contact_name,
        ccc.contact_phone,
        su.nick_name currentUserName,
        cs.name seaName,
        cs.rule,
        cs.manage_id,
        cs.dept_ids,
        source.name source_name
        from cus_customer_or_clue ccoc
        left join cus_cc_contact ccc on ccoc.main_contact_id = ccc.id
        left join sys_user su on ccoc.current_user_id = su.user_id
        left join cus_sea cs on ccoc.sea_id = cs.id
        left join cus_source source on ccoc.source_id = source.id
        <where>
            ccoc.is_deleted = 0
            and ccoc.type = '0'
            and ccoc.is_sea = '1'
            and cs.status = '1'
            <if test="query.createBy != null and query.createBy != ''">
                and ccoc.create_by like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.source != null and query.source != ''">
                and ccoc.source = #{query.source}
            </if>
            <if test="query.sourceId != null">
                and ccoc.source_id = #{query.sourceId}
            </if>
            <if test="query.seaId != null">
                and ccoc.sea_id = #{query.seaId}
            </if>
            <if test="query.startCreateTime != null">
                and ccoc.create_time >= #{query.startCreateTime}
            </if>
            <if test="query.endCreateTime != null">
                and ccoc.create_time &lt;= #{query.endCreateTime}
            </if>
            <if test="query.startLastFollowTime != null">
                and ccoc.last_follow_time >= #{query.startLastFollowTime}
            </if>
            <if test="query.endLastFollowTime != null">
                and ccoc.last_follow_time &lt;= #{query.endLastFollowTime}
            </if>
            <if test="query.currentUserName != null and query.currentUserName != ''">
                and su.nick_name like concat('%', #{query.currentUserName}, '%')
            </if>
            <if test="query.contactName != null and query.contactName != ''">
                and ccc.contact_name like concat('%', #{query.contactName}, '%')
            </if>
            <if test="query.contactPhone != null and query.contactPhone != ''">
                and ccc.contact_phone like concat('%', #{query.contactPhone}, '%')
            </if>
            <if test="query.idList != null and query.idList.size() > 0">
                <foreach collection="query.idList" item="id" open="and ccoc.id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="query.followStatus != null and query.followStatus != ''">
                <choose>
                    <when test='query.followStatus == "3"'>
                        and appeal_flag = 1
                    </when>
                    <otherwise>
                        and ccoc.follow_status = #{query.followStatus} and appeal_flag = 0
                    </otherwise>
                </choose>
            </if>
            <if test="query.remark != null and query.remark != ''">
                and ccoc.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.lastModifiedTimeStart != null and query.lastModifiedTimeEnd != null">
                and ccoc.last_modified_time between #{query.lastModifiedTimeStart} and #{query.lastModifiedTimeEnd}
            </if>
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by ccoc.create_time desc
            </otherwise>
        </choose>
    </select>
    <select id="customerList" resultType="com.jri.biz.cus.domain.vo.CusCustomerListVO">
        select ccoc.*,
        ccc.contact_phone,
        coalesce(product.product_name, type.type_name) as product_name,
        source.name source_name
        from cus_customer_or_clue ccoc
        left join cus_cc_contact ccc on ccoc.main_contact_id = ccc.id
        left join cus_source source on ccoc.source_id = source.id
        left join business_product product on ccoc.product_id = product.id
        left join business_type type on ccoc.product_id = type.id
        <where>
            ccoc.is_deleted = 0
            and ccoc.type = '1'
            and ccoc.is_sea = '0'
            and ccoc.current_user_id = #{query.userId}
            <if test="query.createBy != null and query.createBy != ''">
                and ccoc.create_by like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.source != null and query.source != ''">
                and ccoc.source = #{query.source}
            </if>
            <if test="query.sourceId != null">
                and ccoc.source_id = #{query.sourceId}
            </if>
            <if test="query.followStatus != null and query.followStatus != ''">
                <choose>
                    <when test='query.followStatus == "3"'>
                        and appeal_flag = 1
                    </when>
                    <otherwise>
                        and ccoc.follow_status = #{query.followStatus} and appeal_flag = 0
                    </otherwise>
                </choose>
            </if>
            <if test="query.startCreateTime != null">
                and ccoc.create_time >= #{query.startCreateTime}
            </if>
            <if test="query.endCreateTime != null">
                and ccoc.create_time &lt;= #{query.endCreateTime}
            </if>
            <if test="query.startLastFollowTime != null">
                and ccoc.last_follow_time >= #{query.startLastFollowTime}
            </if>
            <if test="query.endLastFollowTime != null">
                and ccoc.last_follow_time &lt;= #{query.endLastFollowTime}
            </if>
            <if test="query.id != null">
                and ccoc.id = #{query.id}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and (ccoc.company_name like concat('%',#{query.keyword},'%') or
                ccc.contact_phone like concat('%',#{query.keyword},'%') or
                source.name like concat('%',#{query.keyword},'%') or
                product.product_name like concat('%',#{query.keyword},'%') or
                type.type_name like concat('%',#{query.keyword},'%')
                )
            </if>
            <if test="query.companyName != null and query.companyName != ''">
                and ccoc.company_name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.contactPhone != null and query.contactPhone != ''">
                and ccc.contact_phone like concat('%', #{query.contactPhone}, '%')
            </if>
            <if test="query.idList != null and query.idList.size() > 0">
                <foreach collection="query.idList" item="id" open="and ccoc.id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="query.level != null and query.level != ''">
                and ccoc.level = #{query.level}
            </if>
            <if test="query.becomeTimeStart != null and query.becomeTimeEnd != null">
                and ccoc.become_time between #{query.becomeTimeStart} and #{query.becomeTimeEnd}
            </if>
            <if test="query.lastModifiedTimeStart != null and query.lastModifiedTimeEnd != null">
                and ccoc.last_modified_time between #{query.lastModifiedTimeStart} and #{query.lastModifiedTimeEnd}
            </if>
            <if test="query.taxNature != null and query.taxNature != ''">
                and ccoc.tax_nature = #{query.taxNature}
            </if>
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by ccoc.become_time desc
            </otherwise>
        </choose>
    </select>
    <select id="getOtherInfo" resultType="com.jri.biz.cus.domain.request.CusCustomerOtherInfoForm">
        select ccoc.*
        from cus_customer_or_clue ccoc
        where ccoc.id = #{id}
    </select>
    <select id="getOtherInfoByCustomerId" resultType="com.jri.biz.cus.domain.request.CusCustomerOtherInfoForm">
        select ccoc.*
        from cus_customer_or_clue ccoc
        where ccoc.customer_id = #{customerId} and ccoc.is_deleted = 0
        order by ccoc.create_time desc
        limit 1
    </select>
    <select id="customerInSeaList" resultMap="CusCustomerInSeaListVO">
        select ccoc.*,
        ccc.contact_phone,
        cs.name seaName,
        cs.rule,
        cs.manage_id,
        cs.dept_ids,
        source.name source_name
        from cus_customer_or_clue ccoc
        left join cus_cc_contact ccc on ccoc.main_contact_id = ccc.id
        left join cus_sea cs on ccoc.sea_id = cs.id
        left join cus_source source on ccoc.source_id = source.id
        <where>
            ccoc.is_deleted = 0
            and ccoc.type = '1'
            and ccoc.is_sea = '1'
            and cs.status = '1'
            <if test="query.createBy != null and query.createBy != ''">
                and ccoc.create_by like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.source != null and query.source != ''">
                and ccoc.source = #{query.source}
            </if>
            <if test="query.sourceId != null">
                and ccoc.source_id = #{query.sourceId}
            </if>
            <if test="query.followStatus != null and query.followStatus != ''">
                <choose>
                    <when test='query.followStatus == "3"'>
                        and appeal_flag = 1
                    </when>
                    <otherwise>
                        and ccoc.follow_status = #{query.followStatus} and appeal_flag = 0
                    </otherwise>
                </choose>
            </if>
            <if test="query.seaId != null">
                and ccoc.sea_id = #{query.seaId}
            </if>
            <if test="query.startCreateTime != null">
                and ccoc.create_time >= #{query.startCreateTime}
            </if>
            <if test="query.endCreateTime != null">
                and ccoc.create_time &lt;= #{query.endCreateTime}
            </if>
            <if test="query.startLastFollowTime != null">
                and ccoc.last_follow_time >= #{query.startLastFollowTime}
            </if>
            <if test="query.endLastFollowTime != null">
                and ccoc.last_follow_time &lt;= #{query.endLastFollowTime}
            </if>
            <if test="query.companyName != null and query.companyName != ''">
                and ccoc.company_name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.contactPhone != null and query.contactPhone != ''">
                and ccc.contact_phone like concat('%', #{query.contactPhone}, '%')
            </if>
            <if test="query.idList != null and query.idList.size() > 0">
                <foreach collection="query.idList" item="id" open="and ccoc.id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="query.level != null and query.level != ''">
                and ccoc.level = #{query.level}
            </if>
            <if test="query.becomeTimeStart != null and query.becomeTimeEnd != null">
                and ccoc.become_time between #{query.becomeTimeStart} and #{query.becomeTimeEnd}
            </if>
            <if test="query.lastModifiedTimeStart != null and query.lastModifiedTimeEnd != null">
                and ccoc.last_modified_time between #{query.lastModifiedTimeStart} and #{query.lastModifiedTimeEnd}
            </if>
            <if test="query.taxNature != null and query.taxNature != ''">
                and ccoc.tax_nature = #{query.taxNature}
            </if>
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by ccoc.create_time desc
            </otherwise>
        </choose>
    </select>
    <select id="getClueNum" resultType="java.lang.Long">
        select ifnull(count(id), 0)
        from cus_customer_or_clue
        where is_deleted = 0
        and type = '0'
    </select>
    <select id="getCusNum" resultType="java.lang.Long">
        select ifnull(count(id), 0)
        from cus_customer_or_clue
        where is_deleted = 0
        and type = '1'
    </select>
    <select id="getAllClue" resultType="com.jri.biz.cus.domain.entity.CusCustomerOrClue">
        select *
        from cus_customer_or_clue
        where is_deleted = 0
          and type = '0'
        and is_sea = '0'
    </select>
    <select id="getAllCus" resultType="com.jri.biz.cus.domain.entity.CusCustomerOrClue">
        select *
        from cus_customer_or_clue
        where is_deleted = 0
          and type = '1'
          and is_sea = '0'
          and follow_status != '2'
    </select>
    <select id="getChangeNum" resultType="java.lang.Long">
        select ccoc.id
        from cus_customer_or_clue ccoc
        left join cus_cc_business ccb on ccb.cc_id = ccoc.id and ccb.is_deleted = 0
        where ccoc.is_deleted = 0
          and ccoc.type = '1'
        and ccb.stage = '赢单'
        group by ccoc.id
    </select>
    <select id="getChangeNumNoCus" resultType="java.lang.Long">
        select ccoc.id
        from cus_customer_or_clue ccoc
                 left join cus_cc_business ccb on ccb.cc_id = ccoc.id and ccb.is_deleted = 0
                 left join cus_cc_contact ccc on ccoc.id = ccc.cc_id and ccc.is_deleted = 0
        where ccoc.is_deleted = 0
          and ccoc.type = '1'
          and ccb.stage = '赢单'
          and ccc.source = '1'
        group by ccoc.id
    </select>
    <select id="getChangeToCusNum" resultType="java.lang.Long">
        select ifnull(count(id), 0)
        from cus_cc_contact
        where is_deleted = 0
        and source = '1'
    </select>
    <select id="getAddBusinessNum" resultType="java.lang.Long">
        select ccoc.id
        from cus_customer_or_clue ccoc
                 inner join cus_cc_business ccb on ccb.cc_id = ccoc.id and ccb.is_deleted = 0
                left join cus_cc_contact ccc on ccoc.id = ccc.cc_id and ccc.is_deleted = 0
        where ccoc.is_deleted = 0
          and ccoc.type = '1'
          and ccc.source = '1'
        group by ccoc.id
    </select>
    <select id="getClueNumBySource" resultType="java.lang.Long">
        select ifnull(count(id), 0)
        from cus_customer_or_clue
        where is_deleted = 0
          and type = '0'
        and source = #{source}
    </select>
    <select id="getChangeToCusNumBySource" resultType="java.lang.Long">
        select ifnull(count(ccc.id), 0)
        from cus_cc_contact ccc
        where ccc.is_deleted = 0
          and ccc.source = '1'
            and ccc.clue_source = #{source}
    </select>
    <select id="getCompanyNameByCiId" resultType="java.lang.String">
        select company_name
        from cus_customer_or_clue
        where is_deleted = 0
        and type = '1'
        and customer_id = #{customerId}
    </select>
    <select id="allClueList" resultType="com.jri.biz.cus.domain.vo.CusCustomerOrClueListVO">
        select ccoc.*,
        ccc.contact_name,
        ccc.contact_phone,
        ccc.post,
        ccc.wx,
        ccc.qq,
        ccc.sex,
        ccc.email,
        ccc.birthday,
        coalesce(product.product_name, type.type_name) as product_name,
        su.nick_name currentUserName,
        source.name source_name
        from cus_customer_or_clue ccoc
        left join cus_cc_contact ccc on ccoc.main_contact_id = ccc.id
        left join sys_user su on ccoc.current_user_id = su.user_id
        left join sys_dept d on su.dept_id = d.dept_id
        left join cus_source source on ccoc.source_id = source.id
        left join business_product product on ccoc.product_id = product.id
        left join business_type type on ccoc.product_id = type.id
        <where>
            ccoc.is_deleted = 0
            and ccoc.type = '0'
            and ccoc.is_sea = '0'
            <if test="query.createBy != null and query.createBy != ''">
                and ccoc.create_by like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.source != null and query.source != ''">
                and ccoc.source = #{query.source}
            </if>
            <if test="query.sourceId != null">
                and ccoc.source_id = #{query.sourceId}
            </if>
            <if test="query.followStatus != null and query.followStatus != ''">
                <choose>
                    <when test='query.followStatus == "3"'>
                        and appeal_flag = 1
                    </when>
                    <otherwise>
                        and ccoc.follow_status = #{query.followStatus} and appeal_flag = 0
                    </otherwise>
                </choose>
            </if>
            <if test="query.id != null">
                and ccoc.id = #{query.id}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and (ccc.contact_name like concat('%',#{query.keyword},'%') or
                ccc.contact_phone like concat('%',#{query.keyword},'%') or
                source.name like concat('%',#{query.keyword},'%') or
                product.product_name like concat('%',#{query.keyword},'%') or
                type.type_name like concat('%',#{query.keyword},'%'))
            </if>
            <if test="query.currentUserName != null and query.currentUserName != ''">
                and su.nick_name like concat('%',#{query.currentUserName},'%')
            </if>
            <if test="query.contactName != null and query.contactName != ''">
                and ccc.contact_name like concat('%', #{query.contactName}, '%')
            </if>
            <if test="query.contactPhone != null and query.contactPhone != ''">
                and ccc.contact_phone like concat('%', #{query.contactPhone}, '%')
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and ccoc.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.lastFollowTimeStart != null and query.lastFollowTimeEnd != null">
                and ccoc.last_follow_time between #{query.lastFollowTimeStart} and #{query.lastFollowTimeEnd}
            </if>
            <if test="query.idList != null and query.idList.size() > 0">
                <foreach collection="query.idList" item="id" open="and ccoc.id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="query.remark != null and query.remark != ''">
                and ccoc.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.companyName != null and query.companyName !=''">
                and ccoc.company_name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.productId != null">
                and ccoc.product_id = #{query.productId}
            </if>
            <if test="query.taxNature != null and query.taxNature != ''">
                and ccoc.tax_nature = #{query.taxNature}
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${query.params.dataScope}
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by ccoc.create_time desc
            </otherwise>
        </choose>
    </select>
    <select id="allCustomerList" resultType="com.jri.biz.cus.domain.vo.CusCustomerListVO">
        select ccoc.*,
        ccc.contact_phone,
        su.nick_name currentUserName,
        coalesce(product.product_name, type.type_name) as product_name,
        source.name source_name
        from cus_customer_or_clue ccoc
        left join cus_cc_contact ccc on ccoc.main_contact_id = ccc.id
        left join sys_user su on ccoc.current_user_id = su.user_id
        left join sys_dept d on su.dept_id = d.dept_id
        left join cus_source source on ccoc.source_id = source.id
        left join business_product product on ccoc.product_id = product.id
        left join business_type type on ccoc.product_id = type.id
        <where>
            ccoc.is_deleted = 0
            and ccoc.type = '1'
            and ccoc.is_sea = '0'
            <if test="query.createBy != null and query.createBy != ''">
                and ccoc.create_by like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.source != null and query.source != ''">
                and ccoc.source = #{query.source}
            </if>
            <if test="query.sourceId != null">
                and ccoc.source_id = #{query.sourceId}
            </if>
            <if test="query.followStatus != null and query.followStatus != ''">
                <choose>
                    <when test='query.followStatus == "3"'>
                        and appeal_flag = 1
                    </when>
                    <otherwise>
                        and ccoc.follow_status = #{query.followStatus} and appeal_flag = 0
                    </otherwise>
                </choose>
            </if>
            <if test="query.startCreateTime != null">
                and ccoc.create_time >= #{query.startCreateTime}
            </if>
            <if test="query.endCreateTime != null">
                and ccoc.create_time &lt;= #{query.endCreateTime}
            </if>
            <if test="query.startLastFollowTime != null">
                and ccoc.last_follow_time >= #{query.startLastFollowTime}
            </if>
            <if test="query.endLastFollowTime != null">
                and ccoc.last_follow_time &lt;= #{query.endLastFollowTime}
            </if>
            <if test="query.id != null">
                and ccoc.id = #{query.id}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and (ccoc.company_name like concat('%',#{query.keyword},'%') or
                ccc.contact_phone like concat('%',#{query.keyword},'%') or
                source.name like concat('%',#{query.keyword},'%') or
                product.product_name like concat('%',#{query.keyword},'%') or
                type.type_name like concat('%',#{query.keyword},'%'))
            </if>
            <if test="query.companyName != null and query.companyName != ''">
                and ccoc.company_name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.contactPhone != null and query.contactPhone != ''">
                and ccc.contact_phone like concat('%', #{query.contactPhone}, '%')
            </if>
            <if test="query.currentUserName != null and query.currentUserName != ''">
                and su.nick_name like concat('%', #{query.currentUserName}, '%')
            </if>
            <if test="query.idList != null and query.idList.size() > 0">
                <foreach collection="query.idList" item="id" open="and ccoc.id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="query.level != null and query.level != ''">
                and ccoc.level = #{query.level}
            </if>
            <if test="query.becomeTimeStart != null and query.becomeTimeEnd != null">
                and ccoc.become_time between #{query.becomeTimeStart} and #{query.becomeTimeEnd}
            </if>
            <if test="query.lastModifiedTimeStart != null and query.lastModifiedTimeEnd != null">
                and ccoc.last_modified_time between #{query.lastModifiedTimeStart} and #{query.lastModifiedTimeEnd}
            </if>
            <if test="query.taxNature != null and query.taxNature != ''">
                and ccoc.tax_nature = #{query.taxNature}
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${query.params.dataScope}
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by ccoc.become_time desc
            </otherwise>
        </choose>
    </select>
</mapper>
