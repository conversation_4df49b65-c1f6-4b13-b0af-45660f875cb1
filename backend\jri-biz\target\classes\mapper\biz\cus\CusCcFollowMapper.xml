<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusCcFollowMapper">


    <select id="listPage" resultType="com.jri.biz.cus.domain.vo.CusCcFollowListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.cus.domain.vo.CusCcFollowVO">

    </select>
    <select id="list" resultType="com.jri.biz.cus.domain.entity.CusCcFollow">
        select *
        from cus_cc_follow
        <where>
            is_deleted = 0
            and cc_id = #{query.ccId}
            and biz_type = '0'
            <if test="query.text != null  and query.text != ''">
                and (create_by like concat('%', #{query.text}, '%') or content like concat('%',
                #{query.text}, '%') or contact like concat('%', #{query.text}, '%') or `type` like
                concat('%', #{query.text}, '%'))
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="listBusiness" resultType="com.jri.biz.cus.domain.entity.CusCcFollow">
        select *
        from cus_cc_follow
        <where>
            is_deleted = 0
            and cc_id = #{query.ccId}
            and biz_type = '1'
            <if test="query.text != null  and query.text != ''">
                and (create_by like concat('%', #{query.text}, '%') or content like concat('%',
                #{query.text}, '%') or contact like concat('%', #{query.text}, '%') or `type` like
                concat('%', #{query.text}, '%'))
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="lastBusiness" resultType="com.jri.biz.cus.domain.entity.CusCcFollow">
        select *
        from cus_cc_follow
        <where>
            is_deleted = 0
            and cc_id = #{ccId}
            and biz_type = '1'
        </where>
        order by create_time desc limit 1
    </select>
</mapper>
