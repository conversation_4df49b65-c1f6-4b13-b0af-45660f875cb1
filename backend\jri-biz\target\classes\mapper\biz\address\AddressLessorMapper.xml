<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.address.AddressLessorMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.address.AddressLessorListVO">
        select
            *
        from address_lessor
        <where>
            is_deleted = 0
            <if test="query.lessorName != null and query.lessorName != ''">
                and lessor_name like concat('%', #{query.lessorName}, '%')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.address.AddressLessorVO">
        select
            *
        from address_lessor
        where id = #{id}
    </select>
</mapper>
