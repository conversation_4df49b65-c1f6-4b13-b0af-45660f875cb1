<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusCcBusinessMapper">

    <resultMap id="CusCcBusinessVO" type="com.jri.biz.cus.domain.vo.CusCcBusinessVO"
               autoMapping="true">
        <id property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="name" column="name"/>
        <result property="ccId" column="cc_id"/>
        <result property="expectAmount" column="expect_amount"/>
        <result property="expectTime" column="expect_time"/>
        <result property="stage" column="stage"/>
        <result property="stagePercentage" column="stage_percentage"/>
        <result property="status" column="status"/>
        <result property="actualAmount" column="actual_amount"/>
        <result property="winTime" column="win_time"/>
        <result property="loseTime" column="lose_time"/>
        <result property="reason" column="reason"/>
        <result property="remark" column="remark"/>
        <result property="companyName" column="company_name"/>
        <result property="lastFollowTime" column="last_follow_time"/>
        <result property="currentUserName" column="currentUserName"/>
        <result property="contactName" column="contact_name"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerNo" column="customer_no"/>
        <result property="paymentIds" column="payment_ids"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="listPage" resultType="com.jri.biz.cus.domain.vo.CusCcBusinessListVO">
        select ccb.*,
               ccoc.company_name,
               ccoc.current_user_id,
               su.nick_name currentUserName
        from cus_cc_business ccb
                 left join cus_customer_or_clue ccoc on ccb.cc_id = ccoc.id
                 left join sys_user su on ccoc.current_user_id = su.user_id
        <where>
            ccb.is_deleted = 0
            and ccoc.current_user_id = #{query.userId}
            and ccoc.is_sea = '0'
            <if test="query.name != null and query.name != ''">
                and ccb.name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.companyName != null and query.companyName != ''">
                and ccoc.company_name like concat('%', #{query.companyName}, '%')
            </if>
            <if test="query.stage != null and query.stage != ''">
                and ccb.stage = #{query.stage}
            </if>
            <if test="query.followStatus != null and query.followStatus != ''">
                and ccb.follow_status = #{query.followStatus}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="getDetailById" resultMap="CusCcBusinessVO">
        select ccb.*,
               ccoc.company_name,
               ccc.contact_name,
               ccc.contact_phone,
               su.nick_name currentUserName,
               ci.customer_no,
               ci.customer_id
        from cus_cc_business ccb
        left join cus_customer_or_clue ccoc on ccb.cc_id = ccoc.id
        left join sys_user su on ccoc.current_user_id = su.user_id
        left join cus_cc_contact ccc on ccoc.main_contact_id = ccc.id
        left join customer_information ci on ccoc.customer_id = ci.customer_id
        where ccb.id = #{id}
    </select>
    <select id="listByCcId" resultType="com.jri.biz.cus.domain.vo.CustomerBusinessListVO">
        select *
        from cus_cc_business
        where is_deleted = 0 and cc_id = #{ccId}
        order by create_time desc
    </select>
    <select id="getWinNum" resultType="java.lang.Long">
        select count(id)
        from cus_cc_business
        where is_deleted = 0 and cc_id = #{id} and stage = '赢单'
    </select>
    <select id="getLoseNum" resultType="java.lang.Long">
        select count(id)
        from cus_cc_business
        where is_deleted = 0 and cc_id = #{id} and stage = '输单'
    </select>
    <select id="getOtherNum" resultType="java.lang.Long">
        select count(id)
        from cus_cc_business
        where is_deleted = 0 and cc_id = #{id} and (stage != '输单' and stage != '赢单')
    </select>
    <select id="getNum" resultType="java.lang.Long">
        select ifnull(count(id), 0)
        from cus_cc_business
        where is_deleted = 0
    </select>
    <select id="getNumByStage" resultType="java.lang.Long">
        select ifnull(count(id), 0)
        from cus_cc_business
        where is_deleted = 0
        and stage = #{stage}
    </select>
    <select id="getAmount" resultType="java.lang.Long">
        select ifnull(sum(a.actual_amount), 0)
        from (
                 select ccb.actual_amount
                 from cus_cc_business ccb
                          left join cus_cc_contact ccc on ccb.cc_id = ccc.cc_id and ccc.is_deleted = 0
                 where ccb.is_deleted = 0 and ccb.stage = '赢单' and ccc.source = '1'
                 group by ccb.id
                 ) a

    </select>
    <select id="getAmountBySource" resultType="java.lang.Long">
        select ifnull(sum(a.actual_amount), 0)
        from (
                 select ccb.actual_amount
                 from cus_cc_business ccb
                          left join cus_cc_contact ccc on ccb.cc_id = ccc.cc_id and ccc.is_deleted = 0
                          left join cus_customer_or_clue ccoc on ccb.cc_id = ccoc.id
                 where ccb.is_deleted = 0 and ccb.stage = '赢单' and ccoc.source = #{source} and ccc.source = '1'
                 group by ccb.id
                 ) a
    </select>
    <select id="getAmountByYearAndMonth" resultType="java.lang.Long">
        select ifnull(sum(actual_amount), 0)
        from cus_cc_business
        where is_deleted = 0 and stage = '赢单'
        and year(win_time) = #{year}
        and month(win_time) = #{month}
    </select>
    <select id="getByCustomerId" resultType="com.jri.biz.cus.domain.entity.CusCcBusiness">
        select ccb.*
        from cus_cc_business ccb
        left join cus_customer_or_clue ccoc on ccb.cc_id = ccoc.id
        where ccb.is_deleted = 0 and (ccb.stage = '赢单' or ccb.stage = '已收定金,待打尾款')
        and ccoc.customer_id = #{ciId}
    </select>
    <select id="getCountByYearAndMonth" resultType="java.lang.Long">
        select count(id)
        from cus_cc_business
        where is_deleted = 0 and stage = '赢单'
            and year(win_time) = #{year}
          and month(win_time) = #{month}
    </select>
    <select id="getPaymentNoById" resultType="java.lang.String">
        select payment_no
        from finance_payment where id = #{id}
    </select>
</mapper>
