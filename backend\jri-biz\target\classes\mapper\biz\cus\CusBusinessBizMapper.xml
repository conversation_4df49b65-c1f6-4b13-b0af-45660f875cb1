<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusBusinessBizMapper">


    <select id="listPage" resultType="com.jri.biz.cus.domain.vo.CusBusinessBizListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.cus.domain.vo.CusBusinessBizVO">

    </select>
    <select id="getAllBiz" resultType="com.jri.biz.cus.domain.vo.CusBusinessBizVO">
        select cbz.*,
               bt.type_name,
               bp.product_name
        from cus_business_biz cbz
        left join business_product bp on cbz.product_id = bp.id
        left join cus_cc_business ccb on cbz.business_id = ccb.id
        left join cus_customer_or_clue ccoc on ccb.cc_id = ccoc.id
        left join business_type bt on bp.type_id = bt.id
        where ccb.cc_id = #{ccId}
    </select>

    <select id="getAllBizByCustomerId" resultType="com.jri.biz.cus.domain.vo.CusBusinessBizVO">
        select cbz.*,
               bt.type_name,
               bp.product_name
        from cus_business_biz cbz
                 left join business_product bp on cbz.product_id = bp.id
                 left join cus_cc_business ccb on cbz.business_id = ccb.id
                 left join cus_customer_or_clue ccoc on ccb.cc_id = ccoc.id
                 left join business_type bt on bp.type_id = bt.id
        where ccoc.customer_id = #{customerId}
    </select>

    <select id="getBizListByBusinessId" resultType="com.jri.biz.cus.domain.vo.CusBusinessBizVO">
        select cbz.*,
               bt.type_name,
               bp.product_name
        from cus_business_biz cbz
                 left join business_product bp on cbz.product_id = bp.id
                 left join business_type bt on bp.type_id = bt.id
        where cbz.business_id = #{businessId}
    </select>
</mapper>
