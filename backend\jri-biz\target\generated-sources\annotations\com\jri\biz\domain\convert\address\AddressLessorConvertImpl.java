package com.jri.biz.domain.convert.address;

import com.jri.biz.domain.entity.address.AddressLessor;
import com.jri.biz.domain.request.address.AddressLessorForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class AddressLessorConvertImpl implements AddressLessorConvert {

    @Override
    public AddressLessor convert(AddressLessorForm form) {
        if ( form == null ) {
            return null;
        }

        AddressLessor.AddressLessorBuilder addressLessor = AddressLessor.builder();

        addressLessor.id( form.getId() );
        addressLessor.lessorName( form.getLessorName() );
        addressLessor.phone( form.getPhone() );
        addressLessor.bankName( form.getBankName() );
        addressLessor.bankAccount( form.getBankAccount() );

        return addressLessor.build();
    }
}
