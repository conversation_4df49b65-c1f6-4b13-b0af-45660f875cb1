package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcShareholderInfo;
import com.jri.biz.cus.domain.request.CusCcShareholderInfoForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusCcShareholderInfoConvertImpl implements CusCcShareholderInfoConvert {

    @Override
    public CusCcShareholderInfo convert(CusCcShareholderInfoForm form) {
        if ( form == null ) {
            return null;
        }

        CusCcShareholderInfo.CusCcShareholderInfoBuilder cusCcShareholderInfo = CusCcShareholderInfo.builder();

        cusCcShareholderInfo.id( form.getId() );
        cusCcShareholderInfo.shareholderName( form.getShareholderName() );
        cusCcShareholderInfo.shareholderPhone( form.getShareholderPhone() );
        cusCcShareholderInfo.ccId( form.getCcId() );

        return cusCcShareholderInfo.build();
    }
}
