<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.BusinessProductMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.BusinessProductListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.BusinessProductVO">

    </select>
    <select id="getMaxCodeByTypeId" resultType="java.lang.String">
        select lpad(max(substring(code, -2, 2)) + 1, 2, '0') AS max_number
        from business_product
        where is_deleted = 0 and type_id = #{typeId}
    </select>

    <update id="sortReduceOne">

        update business_product
        set sort = sort - 1
        where sort > #{sort}
          and is_deleted = 0

    </update>
    <update id="sortAddOne">
        update business_product
        set sort = sort + 1
        where sort >= #{sort}
          and is_deleted = 0
    </update>

    <select id="getMaxSortId" resultType="java.lang.Long">
        select id
        from business_product
        where is_deleted = 0
        order by sort desc limit 1
    </select>
    <select id="checkSortNum" resultType="java.lang.Integer">
        select count(*)
        from business_product
        where sort >= #{sort}
          and is_deleted = 0
    </select>
    <select id="selectMaxSort" resultType="java.lang.Integer">
        select ifnull(max(sort), 0) + 1
        from business_product
        where is_deleted = 0
    </select>

    <select id="countContractByTypeName" resultType="java.lang.Long">
        select count(*)
        from business_product as product
        left join business_type type on product.type_id = type.id
        left join customer_contract as contract on product.id = contract.product_id
        where contract.contract_status = '1' and contract.is_deleted = '0'
        and type.type_name = #{typeName}
    </select>

    <select id="countContractByProductNameList" resultType="java.lang.Long">
        select count(*)
        from business_product as product
        left join customer_contract as contract on product.id = contract.product_id
        where contract.contract_status = '1' and contract.is_deleted = '0'
        and product.product_name in
        <foreach collection="productNameList" item="productName" open="(" separator="," close=")">
            #{productName}
        </foreach>
    </select>

    <select id="listByTypeName" resultType="com.jri.biz.domain.entity.BusinessProduct">
        select *
        from business_product as product
        left join business_type as type on product.type_id = type.id
        where type.type_name like concat('%', #{typeName}, '%')
    </select>

</mapper>
