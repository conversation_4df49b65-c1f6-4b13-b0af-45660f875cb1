<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusSeaMapper">

    <resultMap id="CusSeaVO" type="com.jri.biz.cus.domain.vo.CusSeaVO"
               autoMapping="true">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="manageId" column="manage_id"/>
        <result property="manageName" column="manageName"/>
        <result property="duration" column="duration"/>
        <result property="remark" column="remark"/>
        <result property="rule" column="rule"/>
        <result property="status" column="status"/>
        <result property="recovery" column="recovery"/>
        <result property="deptIds" column="dept_ids"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <resultMap id="CusSeaListVO" type="com.jri.biz.cus.domain.vo.CusSeaListVO"
               autoMapping="true">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="manageId" column="manage_id"/>
        <result property="manageName" column="manageName"/>
        <result property="duration" column="duration"/>
        <result property="remark" column="remark"/>
        <result property="rule" column="rule"/>
        <result property="status" column="status"/>
        <result property="recovery" column="recovery"/>
        <result property="updateTime" column="update_time"/>
        <result property="deptIds" column="dept_ids"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="listPage" resultMap="CusSeaListVO">
        select cs.*, su.nick_name manageName
        from cus_sea cs
                 left join sys_user su on cs.manage_id = su.user_id
        <where>
            cs.is_deleted = 0
            <if test="query.name != null and query.name != ''">
                and cs.name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.type != null and query.type != ''">
                and cs.type = #{query.type}
            </if>
            <if test="query.status != null and query.status != ''">
                and cs.status = #{query.status}
            </if>
        </where>
        order by cs.create_time desc
    </select>

    <select id="getDetailById" resultMap="CusSeaVO">
        select cs.*, su.nick_name manageName
        from cus_sea cs
        left join sys_user su on cs.manage_id = su.user_id
        where cs.id = #{id}
    </select>
</mapper>
