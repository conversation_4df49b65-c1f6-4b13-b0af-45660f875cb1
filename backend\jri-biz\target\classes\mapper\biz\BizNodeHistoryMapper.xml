<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.BizNodeHistoryMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.BizNodeHistoryListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.BizNodeHistoryVO">

    </select>
    <select id="getListByMainIdAndType" resultType="com.jri.biz.domain.entity.BizNodeHistory">
        select bnh.*, ifnull(su.nick_name, '系统') nickName
        from biz_node_history bnh
        left join sys_user su on bnh.user_id = su.user_id
        where bnh.main_id = #{mainId} and bnh.type = #{type}
    </select>
</mapper>
