<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerBusinessInformationMapper">
    <delete id="deleteById">
        update customer_business_information set is_deleted=1,updateby=#{updateBy} where
            business_information_id=#{id}
    </delete>


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerBusinessInformationListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerBusinessInformationVO">
        select * from customer_business_information where is_deleted=0 and business_information_id= #{id}
    </select>
    <select id="getDetailByCiId" resultType="com.jri.biz.domain.vo.CustomerBusinessInformationVO">
        select * from customer_business_information where is_deleted=0 and ci_id=#{ciId}
    </select>
</mapper>
