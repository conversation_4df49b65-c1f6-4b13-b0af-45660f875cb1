package com.jri.biz.domain.convert.visit;

import com.jri.biz.domain.entity.visit.CustomerVisit;
import com.jri.biz.domain.request.visit.CustomerVisitForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerVisitConvertImpl implements CustomerVisitConvert {

    @Override
    public CustomerVisit convert(CustomerVisitForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerVisit.CustomerVisitBuilder customerVisit = CustomerVisit.builder();

        customerVisit.id( form.getId() );
        customerVisit.planName( form.getPlanName() );
        customerVisit.customerId( form.getCustomerId() );
        customerVisit.planVisitDate( form.getPlanVisitDate() );
        customerVisit.actualPlanDate( form.getActualPlanDate() );
        customerVisit.visitorId( form.getVisitorId() );
        customerVisit.planVisitMethod( form.getPlanVisitMethod() );
        customerVisit.actualVisitMethod( form.getActualVisitMethod() );
        customerVisit.completeTime( form.getCompleteTime() );
        customerVisit.status( form.getStatus() );
        customerVisit.visitPurpose( form.getVisitPurpose() );
        customerVisit.visitFeedback( form.getVisitFeedback() );
        customerVisit.cancelReason( form.getCancelReason() );
        customerVisit.remark( form.getRemark() );
        customerVisit.locationName( form.getLocationName() );
        customerVisit.lng( form.getLng() );
        customerVisit.lat( form.getLat() );

        return customerVisit.build();
    }
}
