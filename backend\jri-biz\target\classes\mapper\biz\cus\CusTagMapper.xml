<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusTagMapper">


    <select id="listPage" resultType="com.jri.biz.cus.domain.vo.CusTagListVO">
        select *
        from cus_tag
        <where>
            is_deleted = 0
            <if test="query.name != null and query.name != ''">
                and `name` like concat('%', #{query.name}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                and status = #{query.status}
            </if>
        </where>
    </select>

    <select id="getDetailById" resultType="com.jri.biz.cus.domain.vo.CusTagVO">
        select *
        from cus_tag
        where id = #{id}
    </select>
</mapper>
