# 三优CRM项目优化实施计划

> 本文档基于 [`feature-compare.md`](./feature-compare.md) 的对比分析结果，提供详细的开发任务清单和实施指导。

## 📊 项目状态概览

| 阶段 | 状态 | 预计工期 | 符合度提升 | 说明 |
|------|------|----------|------------|------|
| 第一阶段：代码重构和冗余清理 | ✅ 已完成 | 2-3周 | 65% → 75% | 冗余模块已清理 |
| 第二阶段：核心缺失功能开发 | ⏳ 待实施 | 4-6周 | 75% → 85% | 市场活动、区域分配等 |
| 第三阶段：业务流程优化 | ⏳ 待实施 | 2-3周 | 85% → 90% | 审核流程、自动分配等 |
| 第四阶段：系统集成和优化 | ⏳ 待实施 | 2-4周 | 90% → 95%+ | ERP集成、性能优化等 |

**当前整体符合度：75%** | **目标符合度：95%+**

---

## 🎯 第一阶段：代码重构和冗余清理 ✅

### ✅ 已完成任务

#### 1. 冗余模块清理
- [x] 删除财务管理模块 (`frontend/src/views/finance/`)
- [x] 删除证书管理模块 (`frontend/src/views/certificate/`)
- [x] 删除材料管理模块 (`frontend/src/views/customer/material-*/`)
- [x] 删除风险管理模块 (`frontend/src/views/customer/risk-*/`)
- [x] 清理相关后端Controller和Service
- [x] 清理相关数据库表

> **详细清理记录**: [`redundant-modules-cleanup-record.md`](./redundant-modules-cleanup-record.md)

### ⏳ 待完成任务

#### 2. 数据模型统一
- [ ] **客户管理统一**
  - [ ] 数据迁移：CustomerInformation → CusCustomerOrClue
  - [ ] 更新后端实体类和Service
  - [ ] 更新前端API调用
  - [ ] 删除冗余的CustomerInformation相关代码

- [ ] **联系人管理统一**
  - [ ] 数据迁移：CustomerContact → CusCcContact
  - [ ] 更新相关业务逻辑
  - [ ] 前端组件统一

- [ ] **跟进记录统一**
  - [ ] 数据迁移：CustomerFollow → CusCcFollow
  - [ ] 统一跟进记录功能
  - [ ] 前端界面调整

---

## 🚀 第二阶段：核心缺失功能开发 ⏳

### 1. 市场活动管理模块（高优先级）

#### 1.1 数据库设计
- [ ] 创建活动分类表 (`market_activity_category`)
- [ ] 创建市场活动表 (`market_activity`)
- [ ] 创建活动参与人员表 (`market_activity_participant`)

#### 1.2 后端开发
- [ ] **实体类开发**
  - [ ] MarketActivityCategory 实体
  - [ ] MarketActivity 实体
  - [ ] MarketActivityParticipant 实体

- [ ] **Controller开发**
  - [ ] MarketActivityCategoryController
  - [ ] MarketActivityController
  - [ ] MarketActivityParticipantController

- [ ] **Service开发**
  - [ ] 活动分类管理服务
  - [ ] 活动管理服务（增删改查）
  - [ ] 活动参与人员管理服务
  - [ ] 活动统计分析服务

#### 1.3 前端开发
- [ ] **页面开发**
  - [ ] 活动分类管理页面
  - [ ] 活动列表页面
  - [ ] 活动详情页面
  - [ ] 活动创建/编辑页面

- [ ] **组件开发**
  - [ ] 活动表单组件
  - [ ] 参与人员管理组件
  - [ ] 活动统计图表组件

- [ ] **功能实现**
  - [ ] 活动导入导出功能
  - [ ] 活动参与人员批量管理
  - [ ] 活动统计报表

### 2. 销售人员管理完善（高优先级）

#### 2.1 区域分配管理
- [ ] **数据库设计**
  - [ ] 创建区域管理表 (`sales_region`)
  - [ ] 创建销售人员区域分配表 (`sales_user_region`)

- [ ] **后端开发**
  - [ ] SalesRegion 实体和服务
  - [ ] SalesUserRegion 实体和服务
  - [ ] 区域分配Controller
  - [ ] 多人负责同一区域的逻辑

- [ ] **前端开发**
  - [ ] 区域管理页面
  - [ ] 销售人员区域分配页面
  - [ ] 区域分配可视化组件

#### 2.2 离职管理功能
- [ ] **数据库设计**
  - [ ] 创建离职交接记录表 (`resignation_handover`)

- [ ] **后端开发**
  - [ ] ResignationHandover 实体和服务
  - [ ] 批量交接逻辑实现
  - [ ] 交接确认流程

- [ ] **前端开发**
  - [ ] 离职管理页面
  - [ ] 批量交接界面
  - [ ] 交接记录查看页面

---

## 🔧 第三阶段：业务流程优化 ⏳

### 1. 客户审核流程
- [ ] **数据库优化**
  - [ ] 为客户表添加审核状态字段
  - [ ] 添加审核相关索引

- [ ] **后端开发**
  - [ ] 审核流程Service
  - [ ] 审核权限控制
  - [ ] 审核历史记录

- [ ] **前端开发**
  - [ ] 审核流程界面
  - [ ] 审核状态显示
  - [ ] 权限控制逻辑

### 2. 自动分配逻辑
- [ ] **算法设计**
  - [ ] 基于区域的自动分配算法
  - [ ] 负载均衡策略
  - [ ] 分配历史记录

- [ ] **后端实现**
  - [ ] 自动分配Service
  - [ ] 分配策略配置
  - [ ] 分配结果通知

- [ ] **前端界面**
  - [ ] 分配策略配置页面
  - [ ] 分配结果查看
  - [ ] 手动调整界面

### 3. 管线配置功能
- [ ] **数据库设计**
  - [ ] 创建销售管线配置表 (`sales_pipeline`)

- [ ] **后端开发**
  - [ ] SalesPipeline 实体和服务
  - [ ] 管线配置Controller

- [ ] **前端开发**
  - [ ] 管线配置管理页面
  - [ ] 管线可视化组件

---

## 🔗 第四阶段：系统集成和优化 ⏳

### 1. ERP系统集成
- [ ] **用户授权集成**
  - [ ] ERP登录接口对接
  - [ ] 用户信息同步
  - [ ] 权限映射

- [ ] **数据同步**
  - [ ] 销售人员信息同步
  - [ ] 组织架构同步
  - [ ] 定时同步任务

### 2. 性能优化
- [ ] **数据库优化**
  - [ ] 索引优化
  - [ ] 查询优化
  - [ ] 分页优化

- [ ] **前端优化**
  - [ ] 组件懒加载
  - [ ] 数据缓存
  - [ ] 打包优化

### 3. 用户体验优化
- [ ] **界面优化**
  - [ ] 响应式设计
  - [ ] 加载状态优化
  - [ ] 错误处理优化

- [ ] **交互优化**
  - [ ] 快捷键支持
  - [ ] 批量操作
  - [ ] 操作反馈

---

## 📱 基于原型的前端重构任务

### 1. 客户详情页面重构（高优先级）

#### 1.1 布局重构
- [ ] **三栏布局实现**
  - [ ] 左侧导航菜单
  - [ ] 中间主内容区
  - [ ] 右侧跟进记录

- [ ] **组件拆分**
  - [ ] CustomerHeader 组件
  - [ ] CustomerForm 组件
  - [ ] FollowRecords 组件
  - [ ] StatusTags 组件
  - [ ] ContactModal 组件

#### 1.2 样式优化
- [ ] **设计规范统一**
  - [ ] 颜色规范
  - [ ] 字体规范
  - [ ] 间距规范
  - [ ] 阴影和圆角规范

- [ ] **响应式设计**
  - [ ] 桌面端适配
  - [ ] 平板端适配
  - [ ] 移动端适配

#### 1.3 交互优化
- [ ] **用户体验提升**
  - [ ] 加载骨架屏
  - [ ] 操作反馈动画
  - [ ] 错误提示优化
  - [ ] 表单验证优化

### 2. 其他页面优化（中优先级）
- [ ] **线索管理页面**
- [ ] **客户列表页面**
- [ ] **数据看板页面**
- [ ] **系统设置页面**

---

## 🗃️ 数据库迁移和优化脚本

### 1. 数据迁移脚本

#### 客户数据统一迁移
```sql
-- 将CustomerInformation数据迁移到CusCustomerOrClue
INSERT INTO cus_customer_or_clue (
    company_name, type, current_user_id, create_time, update_time
) 
SELECT 
    customer_name, '1' as type, manger, create_time, update_time
FROM customer_information 
WHERE is_deleted = 0;
```

#### 联系人数据统一迁移
```sql
-- 将CustomerContact数据迁移到CusCcContact
INSERT INTO cus_cc_contact (
    cc_id, contact_name, contact_phone, post, create_time
)
SELECT 
    ci_id, name, phone, dept, create_time
FROM customer_contact
WHERE is_deleted = 0;
```

### 2. 新增表结构

> **注意**: 完整的建表SQL请参考文档末尾的附录部分

- [ ] 市场活动相关表（3个表）
- [ ] 销售区域管理表（2个表）
- [ ] 离职交接管理表（1个表）
- [ ] 管线配置表（1个表）

### 3. 现有表结构优化
```sql
-- 为客户线索表添加审核相关字段
ALTER TABLE `cus_customer_or_clue` 
ADD COLUMN `audit_status` varchar(20) DEFAULT 'pending' COMMENT '审核状态',
ADD COLUMN `audit_by` varchar(64) COMMENT '审核人',
ADD COLUMN `audit_time` datetime COMMENT '审核时间',
ADD COLUMN `audit_remark` varchar(500) COMMENT '审核备注',
ADD COLUMN `auto_assigned` tinyint(1) DEFAULT 0 COMMENT '是否自动分配',
ADD COLUMN `pipeline_id` bigint(20) COMMENT '管线阶段ID';
```

---

## 🚀 快速开始指南

### 环境准备
1. **备份现有数据库**
   ```bash
   mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql
   ```

2. **准备测试环境**
   - 创建开发分支
   - 配置测试数据库
   - 确认依赖版本

### 实施步骤

#### 第一步：完成数据模型统一
```bash
# 1. 执行数据迁移脚本
mysql -u username -p database_name < migration_scripts.sql

# 2. 验证数据迁移结果
mysql -u username -p database_name -e "SELECT COUNT(*) FROM cus_customer_or_clue;"

# 3. 更新后端实体类
# 4. 更新前端API调用
# 5. 测试功能完整性
```

#### 第二步：开发市场活动模块
```bash
# 1. 创建新的数据表
mysql -u username -p database_name < market_activity_tables.sql

# 2. 生成后端代码框架
# 3. 开发前端页面
# 4. 集成测试
```

#### 第三步：完善销售人员管理
```bash
# 1. 创建区域和离职管理表
# 2. 开发相关功能
# 3. 测试验证
```

---

## 📋 开发检查清单

### 后端开发检查清单

#### 数据库层
- [ ] 表结构设计合理
- [ ] 索引配置正确
- [ ] 外键关系正确
- [ ] 数据迁移脚本测试通过

#### 实体层
- [ ] 实体类字段完整
- [ ] 注解配置正确
- [ ] 关联关系正确
- [ ] 验证规则完整

#### 服务层
- [ ] 业务逻辑正确
- [ ] 异常处理完善
- [ ] 事务配置正确
- [ ] 权限控制到位

#### 控制器层
- [ ] API接口设计合理
- [ ] 参数验证完整
- [ ] 返回格式统一
- [ ] 错误处理完善

### 前端开发检查清单

#### 页面层
- [ ] 页面布局符合原型
- [ ] 响应式设计完整
- [ ] 交互逻辑正确
- [ ] 错误处理完善

#### 组件层
- [ ] 组件设计合理
- [ ] 可复用性良好
- [ ] 属性定义清晰
- [ ] 事件处理正确

#### 数据层
- [ ] API调用正确
- [ ] 数据格式统一
- [ ] 状态管理合理
- [ ] 缓存策略正确

#### 样式层
- [ ] 设计规范统一
- [ ] 兼容性良好
- [ ] 性能优化到位
- [ ] 可维护性强

---

## 🧪 测试计划

### 单元测试
- [ ] **后端单元测试**
  - [ ] Service层测试
  - [ ] Controller层测试
  - [ ] 工具类测试

- [ ] **前端单元测试**
  - [ ] 组件测试
  - [ ] 工具函数测试
  - [ ] 状态管理测试

### 集成测试
- [ ] **API集成测试**
  - [ ] 接口功能测试
  - [ ] 数据一致性测试
  - [ ] 权限控制测试

- [ ] **前后端集成测试**
  - [ ] 页面功能测试
  - [ ] 数据流测试
  - [ ] 用户体验测试

### 系统测试
- [ ] **功能测试**
  - [ ] 核心功能测试
  - [ ] 边界条件测试
  - [ ] 异常情况测试

- [ ] **性能测试**
  - [ ] 响应时间测试
  - [ ] 并发性能测试
  - [ ] 数据库性能测试

- [ ] **兼容性测试**
  - [ ] 浏览器兼容性
  - [ ] 设备兼容性
  - [ ] 分辨率适配

---

## 📊 进度跟踪

### 里程碑计划

| 里程碑 | 预计完成时间 | 主要交付物 | 负责人 |
|--------|--------------|------------|--------|
| M1: 数据模型统一 | Week 2 | 统一的数据模型和迁移脚本 | 后端团队 |
| M2: 市场活动模块 | Week 6 | 完整的市场活动管理功能 | 全栈团队 |
| M3: 销售人员管理 | Week 8 | 区域分配和离职管理功能 | 全栈团队 |
| M4: 业务流程优化 | Week 10 | 审核流程和自动分配功能 | 全栈团队 |
| M5: 系统集成优化 | Week 12 | ERP集成和性能优化 | 全栈团队 |

### 风险控制

#### 技术风险
- **数据迁移风险**: 制定详细的回滚计划
- **性能风险**: 提前进行性能测试
- **兼容性风险**: 多环境测试验证

#### 进度风险
- **需求变更风险**: 建立变更控制流程
- **资源不足风险**: 合理分配开发资源
- **技术难点风险**: 提前进行技术预研

---

## 📚 附录：完整建表SQL

### 市场活动相关表
```sql
-- 活动分类表
CREATE TABLE `market_activity_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `category_type` varchar(20) NOT NULL COMMENT '活动类型：online-线上，offline-线下',
  `sort_order` int(4) DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '1' COMMENT '状态：0-停用，1-启用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='市场活动分类表';

-- 市场活动表
CREATE TABLE `market_activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `activity_name` varchar(100) NOT NULL COMMENT '活动名称',
  `category_id` bigint(20) NOT NULL COMMENT '活动分类ID',
  `activity_time` datetime COMMENT '活动时间',
  `activity_location` varchar(200) COMMENT '活动地点',
  `activity_type` varchar(20) COMMENT '活动方式',
  `organizer` varchar(100) COMMENT '组织者',
  `participant_count` int(11) DEFAULT 0 COMMENT '参与人数',
  `description` text COMMENT '活动描述',
  `status` char(1) DEFAULT '1' COMMENT '状态：0-已取消，1-进行中，2-已结束',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_activity_time` (`activity_time`)
) ENGINE=InnoDB COMMENT='市场活动表';

-- 活动参与人员表
CREATE TABLE `market_activity_participant` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '参与记录ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `customer_id` bigint(20) COMMENT '客户ID',
  `contact_id` bigint(20) COMMENT '联系人ID',
  `participant_name` varchar(50) COMMENT '参与者姓名',
  `participant_phone` varchar(20) COMMENT '参与者电话',
  `participant_company` varchar(100) COMMENT '参与者公司',
  `registration_time` datetime COMMENT '报名时间',
  `attendance_status` char(1) DEFAULT '0' COMMENT '出席状态：0-未出席，1-已出席',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB COMMENT='活动参与人员表';
```

### 销售区域管理表
```sql
-- 区域管理表
CREATE TABLE `sales_region` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '区域ID',
  `region_name` varchar(100) NOT NULL COMMENT '区域名称',
  `region_code` varchar(50) COMMENT '区域编码',
  `country` varchar(50) COMMENT '国家',
  `province` varchar(50) COMMENT '省份',
  `city` varchar(50) COMMENT '城市',
  `district` varchar(50) COMMENT '区县',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父区域ID',
  `level` int(4) DEFAULT 1 COMMENT '区域层级',
  `sort_order` int(4) DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '1' COMMENT '状态：0-停用，1-启用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_region_code` (`region_code`)
) ENGINE=InnoDB COMMENT='销售区域管理表';

-- 销售人员区域分配表
CREATE TABLE `sales_user_region` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分配ID',
  `user_id` bigint(20) NOT NULL COMMENT '销售人员ID',
  `region_id` bigint(20) NOT NULL COMMENT '区域ID',
  `is_primary` tinyint(1) DEFAULT 0 COMMENT '是否主要负责：0-否，1-是',
  `assign_time` datetime COMMENT '分配时间',
  `assign_by` varchar(64) COMMENT '分配人',
  `status` char(1) DEFAULT '1' COMMENT '状态：0-停用，1-启用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_region` (`user_id`, `region_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_region_id` (`region_id`)
) ENGINE=InnoDB COMMENT='销售人员区域分配表';
```

### 离职交接管理表
```sql
-- 离职交接记录表
CREATE TABLE `resignation_handover` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '交接记录ID',
  `from_user_id` bigint(20) NOT NULL COMMENT '离职员工ID',
  `to_user_id` bigint(20) NOT NULL COMMENT '接收员工ID',
  `handover_type` varchar(20) NOT NULL COMMENT '交接类型：customer-客户，clue-线索，region-区域',
  `resource_id` bigint(20) NOT NULL COMMENT '资源ID',
  `resource_name` varchar(200) COMMENT '资源名称',
  `handover_time` datetime COMMENT '交接时间',
  `handover_reason` varchar(500) COMMENT '交接原因',
  `handover_status` char(1) DEFAULT '0' COMMENT '交接状态：0-待确认，1-已确认，2-已拒绝',
  `confirm_time` datetime COMMENT '确认时间',
  `remark` varchar(500) COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  KEY `idx_from_user` (`from_user_id`),
  KEY `idx_to_user` (`to_user_id`),
  KEY `idx_handover_type` (`handover_type`),
  KEY `idx_resource_id` (`resource_id`)
) ENGINE=InnoDB COMMENT='离职交接记录表';
```

### 管线配置表
```sql
-- 销售管线配置表
CREATE TABLE `sales_pipeline` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管线ID',
  `pipeline_name` varchar(50) NOT NULL COMMENT '管线名称',
  `pipeline_code` varchar(30) NOT NULL COMMENT '管线编码',
  `pipeline_color` varchar(10) COMMENT '管线颜色',
  `pipeline_order` int(4) DEFAULT 0 COMMENT '排序',
  `win_probability` decimal(5,2) DEFAULT 0.00 COMMENT '赢单概率(%)',
  `description` varchar(200) COMMENT '描述',
  `status` char(1) DEFAULT '1' COMMENT '状态：0-停用，1-启用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_pipeline_code` (`pipeline_code`),
  KEY `idx_pipeline_order` (`pipeline_order`)
) ENGINE=InnoDB COMMENT='销售管线配置表';
```

---

## 📞 联系方式

如有任何问题或需要技术支持，请联系：
- **项目负责人**: [姓名]
- **技术负责人**: [姓名]
- **文档维护**: [姓名]

---

*最后更新时间: 2025-01-19*
