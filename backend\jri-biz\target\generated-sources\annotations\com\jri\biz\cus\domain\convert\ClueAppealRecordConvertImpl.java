package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.ClueAppealRecord;
import com.jri.biz.cus.domain.request.ClueAppealRecordForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class ClueAppealRecordConvertImpl implements ClueAppealRecordConvert {

    @Override
    public ClueAppealRecord convert(ClueAppealRecordForm form) {
        if ( form == null ) {
            return null;
        }

        ClueAppealRecord.ClueAppealRecordBuilder clueAppealRecord = ClueAppealRecord.builder();

        clueAppealRecord.clueId( form.getClueId() );
        clueAppealRecord.remark( form.getRemark() );

        return clueAppealRecord.build();
    }
}
