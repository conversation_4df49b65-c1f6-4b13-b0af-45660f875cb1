package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.OrderType;
import com.jri.biz.domain.request.OrderTypeForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: java<PERSON>, environment: Java 17.0.16 (Microsoft)"
)
public class OrderTypeConvertImpl implements OrderTypeConvert {

    @Override
    public OrderType convert(OrderTypeForm form) {
        if ( form == null ) {
            return null;
        }

        OrderType.OrderTypeBuilder orderType = OrderType.builder();

        orderType.id( form.getId() );
        orderType.typeName( form.getTypeName() );
        orderType.sort( form.getSort() );
        orderType.status( form.getStatus() );
        orderType.supplementExplain( form.getSupplementExplain() );
        orderType.remark( form.getRemark() );
        orderType.parentId( form.getParentId() );
        orderType.ancestors( form.getAncestors() );

        return orderType.build();
    }
}
