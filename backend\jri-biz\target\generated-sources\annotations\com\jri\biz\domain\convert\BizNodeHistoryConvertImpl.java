package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BizNodeHistory;
import com.jri.biz.domain.request.BizNodeHistoryForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BizNodeHistoryConvertImpl implements BizNodeHistoryConvert {

    @Override
    public BizNodeHistory convert(BizNodeHistoryForm form) {
        if ( form == null ) {
            return null;
        }

        BizNodeHistory.BizNodeHistoryBuilder bizNodeHistory = BizNodeHistory.builder();

        return bizNodeHistory.build();
    }
}
