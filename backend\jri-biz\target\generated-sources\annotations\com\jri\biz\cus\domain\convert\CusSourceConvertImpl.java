package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSource;
import com.jri.biz.cus.domain.request.CusSourceBaseForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusSourceConvertImpl implements CusSourceConvert {

    @Override
    public CusSource convert(CusSourceBaseForm form) {
        if ( form == null ) {
            return null;
        }

        CusSource.CusSourceBuilder cusSource = CusSource.builder();

        cusSource.id( form.getId() );
        cusSource.parentId( form.getParentId() );
        cusSource.biz( form.getBiz() );
        cusSource.name( form.getName() );
        cusSource.sort( form.getSort() );
        cusSource.remark( form.getRemark() );
        cusSource.enable( form.getEnable() );

        return cusSource.build();
    }
}
