<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.UserSuggestionMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.UserSuggestionListVO">
        select
            suggestion.id,
            suggestion.user_id,
            suggestion.customer_id,
            suggestion.publish_time,
            suggestion.suggestion_data,
            suggestion.handle_status,
            suggestion.response_user_id,
            suggestion.response_time,
            suggestion.response_data,
            suggestion.star,
            info.customer_name,
            user.nick_name as user_name,
            response_user.nick_name as response_user_name
        from user_suggestion suggestion
        left join customer_information info on suggestion.customer_id = info.customer_id
        left join sys_user user on suggestion.user_id = user.user_id
        left join sys_user response_user on suggestion.response_user_id = response_user.user_id
        <where>
            <if test="query.userName != null and query.userName != ''">
                and user.nick_name like concat('%', #{query.userName}, '%')
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                and info.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.handleStatus != null and query.handleStatus != ''">
                and suggestion.handle_status = #{query.handleStatus}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and (
                    suggestion.suggestion_data like concat('%', #{query.keyword}, '%') or
                info.customer_name like concat('%', #{query.customerName}, '%')
                )
            </if>
            <if test="query.userId != null">
                and suggestion.user_id = #{query.userId}
            </if>
        </where>
        order by suggestion.handle_status desc, suggestion.publish_time desc
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.UserSuggestionVO">
        select
            suggestion.id,
            suggestion.user_id,
            suggestion.customer_id,
            suggestion.publish_time,
            suggestion.suggestion_data,
            suggestion.handle_status,
            suggestion.response_user_id,
            suggestion.response_time,
            suggestion.response_data,
            suggestion.star,
            info.customer_name,
            user.nick_name as user_name,
            response_user.nick_name as response_user_name
        from user_suggestion suggestion
        left join customer_information info on suggestion.customer_id = info.customer_id
        left join sys_user user on suggestion.user_id = user.user_id
        left join sys_user response_user on suggestion.response_user_id = response_user.user_id
        where suggestion.id = #{id}
    </select>
</mapper>
