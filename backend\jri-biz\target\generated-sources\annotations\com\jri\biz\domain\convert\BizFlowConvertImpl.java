package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BizFlow;
import com.jri.biz.domain.request.BizFlowForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BizFlowConvertImpl implements BizFlowConvert {

    @Override
    public BizFlow convert(BizFlowForm form) {
        if ( form == null ) {
            return null;
        }

        BizFlow.BizFlowBuilder bizFlow = BizFlow.builder();

        bizFlow.id( form.getId() );
        bizFlow.name( form.getName() );
        bizFlow.contractType( form.getContractType() );
        bizFlow.remark( form.getRemark() );
        bizFlow.type( form.getType() );
        bizFlow.enable( form.getEnable() );
        bizFlow.deptIds( form.getDeptIds() );
        bizFlow.xmlStr( form.getXmlStr() );

        return bizFlow.build();
    }
}
