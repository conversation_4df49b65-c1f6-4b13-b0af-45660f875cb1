<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.OrderTypeMapper">
    <update id="updateStatusNormal">
        update order_type set status = '1' where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="listPage" resultType="com.jri.biz.domain.vo.OrderTypeListVO">
        select *
        from order_type
        <where>
            is_deleted = 0
            <if test="query.typeName != null and query.typeName != ''">
                and type_name like concat('%', #{query.typeName}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                and status = #{query.status}
            </if>
        </where>
        order by sort, create_time desc
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.OrderTypeVO">
        select *
        from order_type
        where id = #{id}
    </select>
    <select id="getList" resultType="com.jri.biz.domain.vo.OrderTypeListVO">
        select *
        from order_type
        where
            is_deleted = 0
            and status = '1'
        order by sort, create_time desc
    </select>
    <select id="selectChildrenById" resultType="com.jri.biz.domain.entity.OrderType">
        select *
        from order_type
        where is_deleted = 0 and find_in_set(#{id}, ancestors)
    </select>
    <select id="selectNormalChildrenById" resultType="java.lang.Integer">
        select count(*) from order_type where status = '1' and is_deleted = 0 and find_in_set(#{id}, ancestors)
    </select>
    <select id="getListAll" resultType="com.jri.biz.domain.vo.OrderTypeListVO">
        select *
        from order_type
        <where>
            is_deleted = 0
            <if test="query.typeName != null and query.typeName != ''">
                and type_name like concat('%', #{query.typeName}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                and status = #{query.status}
            </if>
        </where>
        order by sort, create_time desc
    </select>
</mapper>
