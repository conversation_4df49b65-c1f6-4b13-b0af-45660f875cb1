package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerTaxExportDetail;
import com.jri.biz.domain.request.CustomerTaxExportDetailForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerTaxExportDetailConvertImpl implements CustomerTaxExportDetailConvert {

    @Override
    public CustomerTaxExportDetail convert(CustomerTaxExportDetailForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerTaxExportDetail.CustomerTaxExportDetailBuilder customerTaxExportDetail = CustomerTaxExportDetail.builder();

        customerTaxExportDetail.id( form.getId() );
        customerTaxExportDetail.date( form.getDate() );
        customerTaxExportDetail.customsDeclarationNumber( form.getCustomsDeclarationNumber() );
        customerTaxExportDetail.tradeName( form.getTradeName() );
        customerTaxExportDetail.number( form.getNumber() );
        customerTaxExportDetail.unitFirst( form.getUnitFirst() );
        customerTaxExportDetail.supplySource( form.getSupplySource() );
        customerTaxExportDetail.receiptInvoiceNumber( form.getReceiptInvoiceNumber() );
        customerTaxExportDetail.receiptTradeName( form.getReceiptTradeName() );
        customerTaxExportDetail.receiptNumber( form.getReceiptNumber() );
        customerTaxExportDetail.unit( form.getUnit() );
        customerTaxExportDetail.remainder( form.getRemainder() );
        customerTaxExportDetail.declareFlag( form.getDeclareFlag() );
        customerTaxExportDetail.taxRebateFlag( form.getTaxRebateFlag() );
        customerTaxExportDetail.customsDeclarationFlag( form.getCustomsDeclarationFlag() );
        customerTaxExportDetail.billLadingFlag( form.getBillLadingFlag() );
        customerTaxExportDetail.exportContractFlag( form.getExportContractFlag() );
        customerTaxExportDetail.importContractFlag( form.getImportContractFlag() );
        customerTaxExportDetail.paperlessCertificateFlag( form.getPaperlessCertificateFlag() );
        customerTaxExportDetail.packingListFlag( form.getPackingListFlag() );
        customerTaxExportDetail.deliveryNoteFlag( form.getDeliveryNoteFlag() );
        customerTaxExportDetail.proformaInvoiceFlag( form.getProformaInvoiceFlag() );
        customerTaxExportDetail.forwarderInvoiceFlag( form.getForwarderInvoiceFlag() );
        customerTaxExportDetail.collectionStatementFlag( form.getCollectionStatementFlag() );
        customerTaxExportDetail.paymentStatementFlag( form.getPaymentStatementFlag() );
        customerTaxExportDetail.forwarderInvoiceStatementFlag( form.getForwarderInvoiceStatementFlag() );
        customerTaxExportDetail.mainId( form.getMainId() );

        return customerTaxExportDetail.build();
    }
}
