<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.BusinessTypeMapper">
    <update id="sortReduceOne">

            update business_type
            set sort = sort - 1
            where sort > #{sort}
              and is_deleted = 0

    </update>
    <update id="sortAddOne">
        update business_type
        set sort = sort + 1
        where sort >= #{sort}
          and is_deleted = 0
    </update>


    <select id="listPage" resultType="com.jri.biz.domain.vo.BusinessTypeListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.BusinessTypeVO">

    </select>
    <select id="getList" resultType="com.jri.biz.domain.entity.BusinessType">
        select *
        from business_type
        <where>
            is_deleted = 0
            <if test="query.typeName != null and query.typeName != ''">
                and type_name like concat('%', #{query.typeName}, '%')
            </if>
            <if test="query.contractType != null and query.contractType != ''">
                and find_in_set(#{query.contractType}, contract_type)
            </if>
            <if test="query.enterpriseShowFlag != null">
                and enterprise_show_flag = #{query.enterpriseShowFlag}
            </if>
            <if test="query.enable != null">
                and enable = #{query.enable}
            </if>
        </where>
        order by sort, create_time desc
    </select>
    <select id="getMaxSortId" resultType="java.lang.Long">
        select id
        from business_type
        where is_deleted = 0
        order by sort desc limit 1
    </select>
    <select id="checkSortNum" resultType="java.lang.Integer">
        select count(*)
        from business_type
        where sort >= #{sort}
          and is_deleted = 0
    </select>
    <select id="selectMaxSort" resultType="java.lang.Integer">
        select ifnull(max(sort), 0) + 1
        from business_type
        where is_deleted = 0
    </select>
</mapper>
