package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BorrowRecord;
import com.jri.biz.domain.request.BorrowRecordForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BorrowRecordConvertImpl implements BorrowRecordConvert {

    @Override
    public BorrowRecord convert(BorrowRecordForm form) {
        if ( form == null ) {
            return null;
        }

        BorrowRecord.BorrowRecordBuilder borrowRecord = BorrowRecord.builder();

        borrowRecord.id( form.getId() );
        borrowRecord.mainId( form.getMainId() );
        borrowRecord.userId( form.getUserId() );
        borrowRecord.expirationTime( form.getExpirationTime() );
        borrowRecord.borrowReason( form.getBorrowReason() );

        return borrowRecord.build();
    }
}
