package com.jri.biz.domain.convert.address;

import com.jri.biz.domain.entity.address.AddressApply;
import com.jri.biz.domain.request.address.AddressApplyForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class AddressApplyConvertImpl implements AddressApplyConvert {

    @Override
    public AddressApply convert(AddressApplyForm form) {
        if ( form == null ) {
            return null;
        }

        AddressApply.AddressApplyBuilder addressApply = AddressApply.builder();

        addressApply.id( form.getId() );
        addressApply.customerId( form.getCustomerId() );
        addressApply.paymentId( form.getPaymentId() );
        addressApply.remark( form.getRemark() );
        addressApply.areaId( form.getAreaId() );
        addressApply.hostingType( form.getHostingType() );

        return addressApply.build();
    }
}
