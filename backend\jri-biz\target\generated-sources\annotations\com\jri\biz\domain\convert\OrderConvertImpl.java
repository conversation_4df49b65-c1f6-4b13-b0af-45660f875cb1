package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.Order;
import com.jri.biz.domain.request.OrderForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: java<PERSON>, environment: Java 17.0.16 (Microsoft)"
)
public class OrderConvertImpl implements OrderConvert {

    @Override
    public Order convert(OrderForm form) {
        if ( form == null ) {
            return null;
        }

        Order.OrderBuilder order = Order.builder();

        order.id( form.getId() );
        order.createBy( form.getCreateBy() );
        order.orderNo( form.getOrderNo() );
        order.ciId( form.getCiId() );
        order.isUrgent( form.getIsUrgent() );
        order.expectTime( form.getExpectTime() );
        order.orderTypeId( form.getOrderTypeId() );
        order.orderTitle( form.getOrderTitle() );
        order.address( form.getAddress() );
        order.content( form.getContent() );
        order.remark( form.getRemark() );
        order.executor( form.getExecutor() );
        order.orderStatus( form.getOrderStatus() );
        order.contractId( form.getContractId() );
        order.administrativeId( form.getAdministrativeId() );

        return order.build();
    }
}
