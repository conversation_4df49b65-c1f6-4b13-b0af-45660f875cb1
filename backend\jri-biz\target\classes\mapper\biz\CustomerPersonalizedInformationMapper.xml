<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerPersonalizedInformationMapper">

    <resultMap id="CustomerPersonalizedInformationVO" type="com.jri.biz.domain.vo.CustomerPersonalizedInformationVO"
               autoMapping="true">
        <id property="id" column="id"/>
        <result property="ciId" column="ci_id"/>
        <result property="personality" column="personality"/>
        <result property="types" column="types"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="tags" column="tags"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <delete id="deleteById">
        update customer_personalized_information set is_deleted='1',update_by=#{updateBy} where bank_id= #{id}
    </delete>

    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerPersonalizedInformationListVO">

    </select>

    <select id="getDetailById" resultMap="CustomerPersonalizedInformationVO">
        select * from customer_personalized_information where id = #{id}
    </select>
    <select id="getByCiId" resultMap="CustomerPersonalizedInformationVO">
        select * from customer_personalized_information where ci_id = #{ciId} and is_deleted = 0
    </select>
</mapper>
