<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusCcTagsMapper">


    <select id="listPage" resultType="com.jri.biz.cus.domain.vo.CusCcTagsListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.cus.domain.vo.CusCcTagsVO">

    </select>
    <select id="getListByCcId" resultType="java.lang.String">
        select ct.name
        from cus_cc_tags cct
                 left join cus_tag ct on cct.tag_id = ct.id
        where cct.cc_id = #{ccId}
    </select>
    <select id="getTagsListByCcId" resultType="com.jri.biz.cus.domain.entity.CusCcTags">
        select cct.*, ct.name
        from cus_cc_tags cct
                 left join cus_tag ct on cct.tag_id = ct.id
        where cct.cc_id = #{ccId}
    </select>

</mapper>
