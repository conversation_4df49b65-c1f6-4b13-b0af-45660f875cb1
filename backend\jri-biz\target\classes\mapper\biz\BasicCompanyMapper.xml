<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.BasicCompanyMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.BasicCompanyListVO">
        select *
        from basic_company
        <where>
            is_deleted = 0
            <if test="query.name != null and query.name != ''">
                and `name` like concat('%', #{query.name}, '%')
            </if>
            <if test="query.enable != null and query.enable != ''">
                and enable = #{query.enable}
            </if>
        </where>
        order by sort
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.BasicCompanyVO">
        select *
        from basic_company
        where id = #{id}
    </select>
</mapper>
