<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerDiscardRecordMapper">

    <resultMap id="CustomerDiscardRecordVO" type="com.jri.biz.domain.vo.CustomerDiscardRecordVO">
        <id property="id" column="id" javaType="java.lang.Long"/>
        <result property="ciId" column="ci_id" javaType="java.lang.Long"/>
        <result property="discard" column="discard" javaType="java.lang.Integer"/>
        <result property="discardReason" column="discard_reason" javaType="java.lang.String"/>
    </resultMap>


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerDiscardRecordListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerDiscardRecordVO">

    </select>
    <select id="getDetailByCiId" resultMap="CustomerDiscardRecordVO">
        select id, ci_id, discard, discard_reason
        from customer_discard_record
        where ci_id = #{ciId}
        order by id desc
    </select>
    <select id="getRecentDiscardTime" resultType="java.time.LocalDateTime">
        select create_time
        from customer_discard_record
        where ci_id = #{id}
          and discard = 1
        order by create_time desc limit 1
    </select>
</mapper>
