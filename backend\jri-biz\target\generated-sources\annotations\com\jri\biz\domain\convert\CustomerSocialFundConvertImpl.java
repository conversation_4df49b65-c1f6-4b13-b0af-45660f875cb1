package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerSocialFund;
import com.jri.biz.domain.request.CustomerSocialFundForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerSocialFundConvertImpl implements CustomerSocialFundConvert {

    @Override
    public CustomerSocialFund convert(CustomerSocialFundForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerSocialFund.CustomerSocialFundBuilder customerSocialFund = CustomerSocialFund.builder();

        customerSocialFund.id( form.getId() );
        customerSocialFund.ciId( form.getCiId() );
        customerSocialFund.socialAccountFlag( form.getSocialAccountFlag() );
        customerSocialFund.socialAccount( form.getSocialAccount() );
        customerSocialFund.socialPassword( form.getSocialPassword() );
        customerSocialFund.fundAccountFlag( form.getFundAccountFlag() );
        customerSocialFund.fundAccount( form.getFundAccount() );
        customerSocialFund.fundPassword( form.getFundPassword() );
        customerSocialFund.isDeleted( form.getIsDeleted() );

        return customerSocialFund.build();
    }
}
