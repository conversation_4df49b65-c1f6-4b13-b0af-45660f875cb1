package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerTaxRebateIdentified;
import com.jri.biz.domain.request.CustomerTaxRebateIdentifiedForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerTaxRebateIdentifiedConvertImpl implements CustomerTaxRebateIdentifiedConvert {

    @Override
    public CustomerTaxRebateIdentified convert(CustomerTaxRebateIdentifiedForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerTaxRebateIdentified.CustomerTaxRebateIdentifiedBuilder customerTaxRebateIdentified = CustomerTaxRebateIdentified.builder();

        return customerTaxRebateIdentified.build();
    }
}
