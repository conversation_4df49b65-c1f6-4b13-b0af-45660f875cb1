package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcBusiness;
import com.jri.biz.cus.domain.request.CusCcBusinessForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusCcBusinessConvertImpl implements CusCcBusinessConvert {

    @Override
    public CusCcBusiness convert(CusCcBusinessForm form) {
        if ( form == null ) {
            return null;
        }

        CusCcBusiness.CusCcBusinessBuilder cusCcBusiness = CusCcBusiness.builder();

        cusCcBusiness.id( form.getId() );
        cusCcBusiness.name( form.getName() );
        cusCcBusiness.ccId( form.getCcId() );
        cusCcBusiness.expectAmount( form.getExpectAmount() );
        cusCcBusiness.expectTime( form.getExpectTime() );
        cusCcBusiness.stage( form.getStage() );
        cusCcBusiness.stagePercentage( form.getStagePercentage() );
        cusCcBusiness.status( form.getStatus() );
        cusCcBusiness.actualAmount( form.getActualAmount() );
        cusCcBusiness.reason( form.getReason() );
        cusCcBusiness.remark( form.getRemark() );

        return cusCcBusiness.build();
    }
}
