<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.notification.MessageNotificationMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.notification.MessageNotificationListVO">
        select
        id,
        scope_type,
        scope_depts,
        title,
        content,
        create_by,
        CASE
        WHEN update_time is not null THEN update_time
        ELSE create_time
        END AS create_time,
        update_by,
        update_time,
        is_deleted
        from message_notification
        where is_deleted=0
        <if test="query.title != null and query.title != ''">
            AND title like concat('%', #{query.title}, '%')
        </if>
        <if test="query.createBy != null and query.createBy != ''">
            AND create_by like concat('%', #{query.createBy}, '%')
        </if>
        order by create_time desc
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.notification.MessageNotificationVO">

    </select>
</mapper>
