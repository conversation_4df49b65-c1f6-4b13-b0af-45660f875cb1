<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.BasicTaxBureauMapper">
    <update id="updateStatusNormal">
        update basic_tax_bureau set enable = '1' where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="listPage" resultType="com.jri.biz.domain.vo.BasicTaxBureauListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.BasicTaxBureauVO">
        select *
        from basic_tax_bureau where id = #{id}
    </select>
    <select id="selectChildrenById" resultType="com.jri.biz.domain.entity.BasicTaxBureau">
        select *
        from basic_tax_bureau
        where is_deleted = 0 and find_in_set(#{id}, ancestors)
    </select>
    <select id="getList" resultType="com.jri.biz.domain.vo.BasicTaxBureauListVO">
        select *
        from basic_tax_bureau
        <where>
            is_deleted = 0
            <if test="query.name != null and query.name != ''">
                and `name` like concat('%', #{query.name}, '%')
            </if>
            <if test="query.enable != null and query.enable != ''">
                and enable = #{query.enable}
            </if>
            <if test="query.address != null and query.address != ''">
                and address like concat('%', #{query.address}, '%')
            </if>
        </where>
        order by parent_id, sort
    </select>
    <select id="selectNormalChildrenById" resultType="java.lang.Integer">
        select count(*) from basic_tax_bureau where enable = '1' and is_deleted = 0 and find_in_set(#{id}, ancestors)
    </select>
</mapper>
