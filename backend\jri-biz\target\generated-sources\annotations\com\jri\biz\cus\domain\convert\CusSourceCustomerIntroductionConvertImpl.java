package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSourceCustomerIntroduction;
import com.jri.biz.cus.domain.request.CusSourceCustomerIntroductionForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusSourceCustomerIntroductionConvertImpl implements CusSourceCustomerIntroductionConvert {

    @Override
    public CusSourceCustomerIntroduction convert(CusSourceCustomerIntroductionForm form) {
        if ( form == null ) {
            return null;
        }

        CusSourceCustomerIntroduction.CusSourceCustomerIntroductionBuilder cusSourceCustomerIntroduction = CusSourceCustomerIntroduction.builder();

        cusSourceCustomerIntroduction.customerId( form.getCustomerId() );

        return cusSourceCustomerIntroduction.build();
    }
}
