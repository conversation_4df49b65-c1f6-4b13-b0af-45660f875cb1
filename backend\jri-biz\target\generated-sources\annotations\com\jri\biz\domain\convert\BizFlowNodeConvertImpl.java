package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BizFlowNode;
import com.jri.biz.domain.request.BizFlowNodeForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BizFlowNodeConvertImpl implements BizFlowNodeConvert {

    @Override
    public BizFlowNode convert(BizFlowNodeForm form) {
        if ( form == null ) {
            return null;
        }

        BizFlowNode.BizFlowNodeBuilder bizFlowNode = BizFlowNode.builder();

        return bizFlowNode.build();
    }
}
