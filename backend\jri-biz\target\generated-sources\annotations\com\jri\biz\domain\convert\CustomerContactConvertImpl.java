package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerContact;
import com.jri.biz.domain.request.CustomerContactForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerContactConvertImpl implements CustomerContactConvert {

    @Override
    public CustomerContact convert(CustomerContactForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerContact.CustomerContactBuilder customerContact = CustomerContact.builder();

        customerContact.id( form.getId() );
        customerContact.ciId( form.getCiId() );
        customerContact.name( form.getName() );
        customerContact.phone( form.getPhone() );
        customerContact.dept( form.getDept() );
        customerContact.post( form.getPost() );
        customerContact.wechat( form.getWechat() );
        customerContact.email( form.getEmail() );
        customerContact.isLeader( form.getIsLeader() );
        customerContact.details( form.getDetails() );
        customerContact.createBy( form.getCreateBy() );
        customerContact.createTime( form.getCreateTime() );
        customerContact.isDeleted( form.getIsDeleted() );
        customerContact.isOften( form.getIsOften() );
        customerContact.sex( form.getSex() );
        customerContact.qq( form.getQq() );
        customerContact.birthday( form.getBirthday() );

        return customerContact.build();
    }
}
