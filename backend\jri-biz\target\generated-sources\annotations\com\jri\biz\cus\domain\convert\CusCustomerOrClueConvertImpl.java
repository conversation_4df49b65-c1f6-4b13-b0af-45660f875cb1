package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCustomerOrClue;
import com.jri.biz.cus.domain.request.CusCustomerOrClueForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusCustomerOrClueConvertImpl implements CusCustomerOrClueConvert {

    @Override
    public CusCustomerOrClue convert(CusCustomerOrClueForm form) {
        if ( form == null ) {
            return null;
        }

        CusCustomerOrClue.CusCustomerOrClueBuilder cusCustomerOrClue = CusCustomerOrClue.builder();

        cusCustomerOrClue.id( form.getId() );
        cusCustomerOrClue.source( form.getSource() );
        cusCustomerOrClue.sourceId( form.getSourceId() );
        cusCustomerOrClue.introductionCustomerId( form.getIntroductionCustomerId() );
        cusCustomerOrClue.companyName( form.getCompanyName() );
        cusCustomerOrClue.remark( form.getRemark() );
        cusCustomerOrClue.industry( form.getIndustry() );
        cusCustomerOrClue.area( form.getArea() );
        cusCustomerOrClue.address( form.getAddress() );
        cusCustomerOrClue.taxNature( form.getTaxNature() );
        cusCustomerOrClue.firstFollowTime( form.getFirstFollowTime() );
        cusCustomerOrClue.level( form.getLevel() );
        cusCustomerOrClue.seaId( form.getSeaId() );
        cusCustomerOrClue.isSea( form.getIsSea() );
        cusCustomerOrClue.mainContactId( form.getMainContactId() );
        cusCustomerOrClue.type( form.getType() );
        cusCustomerOrClue.entryType( form.getEntryType() );
        cusCustomerOrClue.phone( form.getPhone() );
        cusCustomerOrClue.productId( form.getProductId() );

        return cusCustomerOrClue.build();
    }
}
