package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerPersonalizedInformation;
import com.jri.biz.domain.request.CustomerPersonalizedInformationForm;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerPersonalizedInformationConvertImpl implements CustomerPersonalizedInformationConvert {

    @Override
    public CustomerPersonalizedInformation convert(CustomerPersonalizedInformationForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerPersonalizedInformation.CustomerPersonalizedInformationBuilder customerPersonalizedInformation = CustomerPersonalizedInformation.builder();

        customerPersonalizedInformation.id( form.getId() );
        customerPersonalizedInformation.ciId( form.getCiId() );
        customerPersonalizedInformation.personality( form.getPersonality() );
        List<String> list = form.getTypes();
        if ( list != null ) {
            customerPersonalizedInformation.types( new ArrayList<String>( list ) );
        }
        List<String> list1 = form.getTags();
        if ( list1 != null ) {
            customerPersonalizedInformation.tags( new ArrayList<String>( list1 ) );
        }
        customerPersonalizedInformation.ageLevel( form.getAgeLevel() );
        customerPersonalizedInformation.personalityComplement( form.getPersonalityComplement() );
        customerPersonalizedInformation.collectionRequirement( form.getCollectionRequirement() );
        customerPersonalizedInformation.dealRequirement( form.getDealRequirement() );
        customerPersonalizedInformation.billingDemand( form.getBillingDemand() );
        customerPersonalizedInformation.isDeleted( form.getIsDeleted() );

        return customerPersonalizedInformation.build();
    }
}
