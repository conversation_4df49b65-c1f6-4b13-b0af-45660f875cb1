package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicBank;
import com.jri.biz.domain.request.BasicBankForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BasicBankConvertImpl implements BasicBankConvert {

    @Override
    public BasicBank convert(BasicBankForm form) {
        if ( form == null ) {
            return null;
        }

        BasicBank.BasicBankBuilder basicBank = BasicBank.builder();

        basicBank.id( form.getId() );
        basicBank.parentId( form.getParentId() );
        basicBank.ancestors( form.getAncestors() );
        basicBank.name( form.getName() );
        basicBank.sort( form.getSort() );
        basicBank.remark( form.getRemark() );
        basicBank.enable( form.getEnable() );
        basicBank.address( form.getAddress() );
        basicBank.lat( form.getLat() );
        basicBank.lng( form.getLng() );

        return basicBank.build();
    }
}
