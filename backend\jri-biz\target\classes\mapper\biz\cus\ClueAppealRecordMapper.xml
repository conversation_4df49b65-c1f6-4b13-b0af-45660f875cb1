<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.ClueAppealRecordMapper">


    <select id="listPage" resultType="com.jri.biz.cus.domain.vo.ClueAppealRecordListVO">
        select
            record.id,
            record.clue_id,
            contact.contact_name clue_name,
            source.name source_name,
            record.status,
            record.remark,
            record.create_user_id,
            user.nick_name create_user_name,
            record.create_by,
            record.create_time
        from clue_appeal_record record
        left join cus_customer_or_clue clue on record.clue_id = clue.id
        left join cus_cc_contact contact on clue.main_contact_id = contact.id
        left join cus_source source on clue.source_id = source.id
        left join sys_user user on record.create_user_id = user.user_id
        left join sys_dept dept on user.dept_id = dept.dept_id
        <where>
            <if test="query.clueName != null and query.clueName != ''">
                and contact.contact_name like concat('%', #{query.clueName}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                and record.status = #{query.status}
            </if>
            <if test="query.createUserName != null and query.createUserName != ''">
                and user.nick_name like concat('%', #{query.createUserName}, '%')
            </if>
            <if test="query.sourceId != null">
                and clue.source_id = #{query.sourceId}
            </if>
            <if test="query.remark != null and query.remark != ''">
                and record.remark like concat('%', #{query.remark}, '%')
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and record.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            ${query.params.dataScope}
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>

            </otherwise>
        </choose>
    </select>

    <select id="getDetailById" resultType="com.jri.biz.cus.domain.vo.ClueAppealRecordVO">
        select
            record.id,
            record.clue_id,
            contact.contact_name clue_name,
            source.name source_name,
            record.status,
            record.remark,
            record.create_user_id,
            user.nick_name create_user_name,
            record.create_by,
            record.create_time
        from clue_appeal_record record
        left join cus_customer_or_clue clue on record.clue_id = clue.id
        left join cus_cc_contact contact on clue.main_contact_id = contact.id
        left join cus_source source on clue.source_id = source.id
        left join sys_user user on record.create_user_id = user.user_id
        where record.id = #{id}
    </select>
</mapper>
