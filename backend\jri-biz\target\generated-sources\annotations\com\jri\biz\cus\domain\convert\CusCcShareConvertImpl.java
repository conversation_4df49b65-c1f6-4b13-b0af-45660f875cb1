package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcShare;
import com.jri.biz.cus.domain.request.CusCcShareForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusCcShareConvertImpl implements CusCcShareConvert {

    @Override
    public CusCcShare convert(CusCcShareForm form) {
        if ( form == null ) {
            return null;
        }

        CusCcShare.CusCcShareBuilder cusCcShare = CusCcShare.builder();

        return cusCcShare.build();
    }
}
