package com.jri.biz.domain.convert.address;

import com.jri.biz.domain.entity.address.AddressPropertyOwnership;
import com.jri.biz.domain.request.address.AddressPropertyOwnershipForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class AddressPropertyOwnershipConvertImpl implements AddressPropertyOwnershipConvert {

    @Override
    public AddressPropertyOwnership convert(AddressPropertyOwnershipForm form) {
        if ( form == null ) {
            return null;
        }

        AddressPropertyOwnership.AddressPropertyOwnershipBuilder addressPropertyOwnership = AddressPropertyOwnership.builder();

        addressPropertyOwnership.id( form.getId() );
        addressPropertyOwnership.name( form.getName() );
        addressPropertyOwnership.propertyCategory( form.getPropertyCategory() );
        addressPropertyOwnership.areaId( form.getAreaId() );
        addressPropertyOwnership.leaseCategory( form.getLeaseCategory() );
        addressPropertyOwnership.leaseStartDate( form.getLeaseStartDate() );
        addressPropertyOwnership.leaseEndDate( form.getLeaseEndDate() );
        addressPropertyOwnership.leasePrice( form.getLeasePrice() );
        addressPropertyOwnership.regin( form.getRegin() );
        addressPropertyOwnership.ownerName( form.getOwnerName() );
        addressPropertyOwnership.ownerPhone( form.getOwnerPhone() );
        addressPropertyOwnership.lessorId( form.getLessorId() );
        addressPropertyOwnership.licensedIndustry( form.getLicensedIndustry() );
        addressPropertyOwnership.prohibitedIndustry( form.getProhibitedIndustry() );
        addressPropertyOwnership.other( form.getOther() );
        addressPropertyOwnership.managementCompanyCategory( form.getManagementCompanyCategory() );
        addressPropertyOwnership.remark( form.getRemark() );

        return addressPropertyOwnership.build();
    }
}
