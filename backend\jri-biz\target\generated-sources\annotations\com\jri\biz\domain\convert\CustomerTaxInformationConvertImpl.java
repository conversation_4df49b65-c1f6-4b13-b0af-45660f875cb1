package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerTaxInformation;
import com.jri.biz.domain.request.CustomerTaxInformationForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerTaxInformationConvertImpl implements CustomerTaxInformationConvert {

    @Override
    public CustomerTaxInformation convert(CustomerTaxInformationForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerTaxInformation.CustomerTaxInformationBuilder customerTaxInformation = CustomerTaxInformation.builder();

        customerTaxInformation.id( form.getId() );
        customerTaxInformation.ciId( form.getCiId() );
        customerTaxInformation.identityNumber( form.getIdentityNumber() );
        customerTaxInformation.taxRegistrationOrgan( form.getTaxRegistrationOrgan() );
        customerTaxInformation.taxOrganAddress( form.getTaxOrganAddress() );
        customerTaxInformation.rateRegistration( form.getRateRegistration() );
        customerTaxInformation.legalRealNameFlag( form.getLegalRealNameFlag() );
        customerTaxInformation.taxRealNameFlag( form.getTaxRealNameFlag() );
        customerTaxInformation.individualCheckFlag( form.getIndividualCheckFlag() );
        customerTaxInformation.onlineRevenueRegistrationFlag( form.getOnlineRevenueRegistrationFlag() );
        customerTaxInformation.reservedPhoneNumber( form.getReservedPhoneNumber() );
        customerTaxInformation.tripleAgreementFlag( form.getTripleAgreementFlag() );
        customerTaxInformation.identificationMethodFlag( form.getIdentificationMethodFlag() );
        customerTaxInformation.certificateAccount( form.getCertificateAccount() );
        customerTaxInformation.certificatePassword( form.getCertificatePassword() );
        customerTaxInformation.drawingSheetFlag( form.getDrawingSheetFlag() );
        customerTaxInformation.invoiceFlag( form.getInvoiceFlag() );
        customerTaxInformation.invoiceSealFlag( form.getInvoiceSealFlag() );
        customerTaxInformation.drawingSheetDept( form.getDrawingSheetDept() );
        customerTaxInformation.invoiceDept( form.getInvoiceDept() );
        customerTaxInformation.invoiceSealDept( form.getInvoiceSealDept() );
        customerTaxInformation.invoiceLimit( form.getInvoiceLimit() );
        customerTaxInformation.invoiceType( form.getInvoiceType() );
        customerTaxInformation.drawingSheetType( form.getDrawingSheetType() );
        customerTaxInformation.createBy( form.getCreateBy() );
        customerTaxInformation.updateBy( form.getUpdateBy() );
        customerTaxInformation.naturalPersonPassword( form.getNaturalPersonPassword() );

        return customerTaxInformation.build();
    }
}
