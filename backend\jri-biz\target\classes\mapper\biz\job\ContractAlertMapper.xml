<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.job.ContractAlertMapper">



    <select id="listPage" resultType="com.jri.biz.domain.vo.job.ContractAlertListVO">
        SELECT
        information.customer_id,
        information.customer_name,
        information.customer_no,
        manager_user.nick_name as manger,
        alert.manger_user_id,
        contract.contract_id,
        contract.contract_name,
        contract.contract_no,
        product.product_name,
        alert.alert_time,
        contract.contract_type,
        contract.total_cost,
        contract.start_time,
        contract.end_time
        FROM
        contract_alert alert
        LEFT JOIN customer_contract contract on alert.contract_id=contract.contract_id
        LEFT JOIN customer_information information ON contract.ci_id = information.customer_id
        AND information.is_deleted =0
        LEFT JOIN contract_alert_job job on job.contract_type = contract.contract_type and job.is_deleted=0 and
        alert_status=1
        LEFT JOIN business_product product on product.id=contract.product_id and product.is_deleted=0
        LEFT JOIN sys_user as manager_user on manager_user.user_id = alert.manger_user_id
        <where>
        <if test="query.contractName!=null and query.contractName!=''">
            and contract.contract_name LIKE concat('%',#{query.contractName},'%')
        </if>
        <if test="query.contractNo!=null and query.contractNo!=''">
            and contract.contract_no LIKE concat('%',#{query.contractNo},'%')
        </if>
        <if test="query.customerName!=null and query.customerName!=''">
            and information.customer_name LIKE concat('%',#{query.customerName},'%')
        </if>
        <if test="query.customerNo!=null and query.customerNo!=''">
            and information.customer_no LIKE concat('%',#{query.customerNo},'%')
        </if>
        </where>
        order by  alert.alert_time desc


    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.job.ContractAlertVO">

    </select>
</mapper>
