package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicBookkeepingSubject;
import com.jri.biz.domain.request.BasicBookkeepingSubjectForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BasicBookkeepingSubjectConvertImpl implements BasicBookkeepingSubjectConvert {

    @Override
    public BasicBookkeepingSubject convert(BasicBookkeepingSubjectForm form) {
        if ( form == null ) {
            return null;
        }

        BasicBookkeepingSubject.BasicBookkeepingSubjectBuilder basicBookkeepingSubject = BasicBookkeepingSubject.builder();

        basicBookkeepingSubject.id( form.getId() );
        basicBookkeepingSubject.parentId( form.getParentId() );
        basicBookkeepingSubject.ancestors( form.getAncestors() );
        basicBookkeepingSubject.name( form.getName() );
        basicBookkeepingSubject.sort( form.getSort() );
        basicBookkeepingSubject.remark( form.getRemark() );
        basicBookkeepingSubject.enable( form.getEnable() );

        return basicBookkeepingSubject.build();
    }
}
