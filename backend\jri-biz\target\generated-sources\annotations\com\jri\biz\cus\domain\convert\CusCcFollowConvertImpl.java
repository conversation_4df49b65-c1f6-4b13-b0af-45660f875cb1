package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcFollow;
import com.jri.biz.cus.domain.request.CusCcFollowForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusCcFollowConvertImpl implements CusCcFollowConvert {

    @Override
    public CusCcFollow convert(CusCcFollowForm form) {
        if ( form == null ) {
            return null;
        }

        CusCcFollow.CusCcFollowBuilder cusCcFollow = CusCcFollow.builder();

        cusCcFollow.id( form.getId() );
        cusCcFollow.content( form.getContent() );
        cusCcFollow.contact( form.getContact() );
        cusCcFollow.type( form.getType() );
        cusCcFollow.ccId( form.getCcId() );

        return cusCcFollow.build();
    }
}
