package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusTag;
import com.jri.biz.cus.domain.request.CusTagForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: java<PERSON>, environment: Java 17.0.16 (Microsoft)"
)
public class CusTagConvertImpl implements CusTagConvert {

    @Override
    public CusTag convert(CusTagForm form) {
        if ( form == null ) {
            return null;
        }

        CusTag.CusTagBuilder cusTag = CusTag.builder();

        cusTag.id( form.getId() );
        cusTag.name( form.getName() );
        cusTag.remark( form.getRemark() );
        cusTag.status( form.getStatus() );

        return cusTag.build();
    }
}
