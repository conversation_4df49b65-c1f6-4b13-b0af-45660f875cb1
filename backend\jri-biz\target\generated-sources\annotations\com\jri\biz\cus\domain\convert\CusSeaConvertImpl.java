package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSea;
import com.jri.biz.cus.domain.request.CusSeaForm;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusSeaConvertImpl implements CusSeaConvert {

    @Override
    public CusSea convert(CusSeaForm form) {
        if ( form == null ) {
            return null;
        }

        CusSea.CusSeaBuilder cusSea = CusSea.builder();

        cusSea.id( form.getId() );
        cusSea.type( form.getType() );
        cusSea.name( form.getName() );
        cusSea.manageId( form.getManageId() );
        cusSea.duration( form.getDuration() );
        cusSea.remark( form.getRemark() );
        cusSea.rule( form.getRule() );
        cusSea.status( form.getStatus() );
        cusSea.recovery( form.getRecovery() );
        List<Long> list = form.getDeptIds();
        if ( list != null ) {
            cusSea.deptIds( new ArrayList<Long>( list ) );
        }

        return cusSea.build();
    }
}
