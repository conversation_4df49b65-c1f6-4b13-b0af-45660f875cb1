package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerInformation;
import com.jri.biz.domain.request.CustomerInformationForm;
import com.jri.biz.domain.vo.CustomerInformationVO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerInformationConvertImpl implements CustomerInformationConvert {

    @Override
    public CustomerInformation convert(CustomerInformationForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerInformation.CustomerInformationBuilder customerInformation = CustomerInformation.builder();

        customerInformation.customerId( form.getCustomerId() );
        customerInformation.customerNo( form.getCustomerNo() );
        customerInformation.customerName( form.getCustomerName() );
        customerInformation.contractMainId( form.getContractMainId() );
        customerInformation.manger( form.getManger() );
        customerInformation.branchOffice( form.getBranchOffice() );
        customerInformation.address( form.getAddress() );
        customerInformation.informationMark( form.getInformationMark() );
        customerInformation.customerStatus( form.getCustomerStatus() );
        customerInformation.customerProperty( form.getCustomerProperty() );
        customerInformation.nofeeReasonMark( form.getNofeeReasonMark() );
        customerInformation.isDeleted( form.getIsDeleted() );
        customerInformation.discard( form.getDiscard() );
        customerInformation.discardReason( form.getDiscardReason() );
        customerInformation.industry( form.getIndustry() );
        customerInformation.nofeeReason( form.getNofeeReason() );
        customerInformation.counselor( form.getCounselor() );
        customerInformation.customerSuccess( form.getCustomerSuccess() );
        customerInformation.sponsorAccounting( form.getSponsorAccounting() );
        customerInformation.mangerUserId( form.getMangerUserId() );
        customerInformation.counselorUserId( form.getCounselorUserId() );
        customerInformation.customerSuccessUserId( form.getCustomerSuccessUserId() );
        customerInformation.sponsorAccountingUserId( form.getSponsorAccountingUserId() );
        customerInformation.lng( form.getLng() );
        customerInformation.lat( form.getLat() );

        return customerInformation.build();
    }

    @Override
    public CustomerInformation convert2(CustomerInformationVO civ) {
        if ( civ == null ) {
            return null;
        }

        CustomerInformation.CustomerInformationBuilder customerInformation = CustomerInformation.builder();

        customerInformation.customerId( civ.getCustomerId() );
        customerInformation.customerNo( civ.getCustomerNo() );
        customerInformation.customerName( civ.getCustomerName() );
        customerInformation.contractMainId( civ.getContractMainId() );
        customerInformation.manger( civ.getManger() );
        customerInformation.branchOffice( civ.getBranchOffice() );
        customerInformation.address( civ.getAddress() );
        customerInformation.informationMark( civ.getInformationMark() );
        customerInformation.customerStatus( civ.getCustomerStatus() );
        customerInformation.customerProperty( civ.getCustomerProperty() );
        customerInformation.nofeeReasonMark( civ.getNofeeReasonMark() );
        customerInformation.createBy( civ.getCreateBy() );
        customerInformation.createTime( civ.getCreateTime() );
        if ( civ.getDiscard() != null ) {
            customerInformation.discard( civ.getDiscard() );
        }
        customerInformation.discardReason( civ.getDiscardReason() );
        customerInformation.industry( civ.getIndustry() );
        customerInformation.nofeeReason( civ.getNofeeReason() );
        customerInformation.counselor( civ.getCounselor() );
        customerInformation.customerSuccess( civ.getCustomerSuccess() );
        customerInformation.sponsorAccounting( civ.getSponsorAccounting() );
        customerInformation.mangerUserId( civ.getMangerUserId() );
        customerInformation.counselorUserId( civ.getCounselorUserId() );
        customerInformation.customerSuccessUserId( civ.getCustomerSuccessUserId() );
        customerInformation.sponsorAccountingUserId( civ.getSponsorAccountingUserId() );
        customerInformation.walletAmount( civ.getWalletAmount() );
        customerInformation.companyIdentification( civ.getCompanyIdentification() );
        customerInformation.lng( civ.getLng() );
        customerInformation.lat( civ.getLat() );

        return customerInformation.build();
    }
}
