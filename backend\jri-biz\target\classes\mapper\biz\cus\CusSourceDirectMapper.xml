<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusSourceDirectMapper">

    <select id="selectListByRecentDate" resultType="com.jri.biz.cus.domain.entity.CusSourceDirect">
        SELECT
            id, main_id, amount, charge_time
        FROM cus_source_direct
        where charge_time in (SELECT
        max(charge_time)
        FROM cus_source_direct
        where main_id = #{mainId} and charge_time <![CDATA[<=]]> #{date}) and main_id = #{mainId}
    </select>

    <select id="getMinChargeTime" resultType="java.time.LocalDate">
        SELECT
            min(charge_time)
        FROM cus_source_direct
        where main_id = #{mainId} and charge_time <![CDATA[>]]> #{date}
    </select>

</mapper>
