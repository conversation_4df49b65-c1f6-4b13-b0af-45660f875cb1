<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CommonBizFileMapper">


        <delete id="deleteByMainIdAndBizType">
            delete
            from common_biz_file
            where main_id = #{mainId} and biz_type = #{bizType}
        </delete>
    <select id="selectByMainIdAndBizType" resultType="com.jri.common.core.domain.CommonBizFile">
            select id,
                   main_id,
                   `name`,
                   location,
                   type,
                   biz_type
            from common_biz_file
            where   is_deleted=0 and main_id = #{mainId} and biz_type = #{bizType}
        </select>
    </mapper>

