package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSourceChannel;
import com.jri.biz.cus.domain.request.CusSourceChannelForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: java<PERSON>, environment: Java 17.0.16 (Microsoft)"
)
public class CusSourceChannelConvertImpl implements CusSourceChannelConvert {

    @Override
    public CusSourceChannel convert(CusSourceChannelForm form) {
        if ( form == null ) {
            return null;
        }

        CusSourceChannel.CusSourceChannelBuilder cusSourceChannel = CusSourceChannel.builder();

        cusSourceChannel.phone( form.getPhone() );
        cusSourceChannel.bankAccount( form.getBankAccount() );

        return cusSourceChannel.build();
    }
}
