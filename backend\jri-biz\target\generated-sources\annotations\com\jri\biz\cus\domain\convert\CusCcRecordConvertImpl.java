package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcRecord;
import com.jri.biz.cus.domain.request.CusCcRecordForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusCcRecordConvertImpl implements CusCcRecordConvert {

    @Override
    public CusCcRecord convert(CusCcRecordForm form) {
        if ( form == null ) {
            return null;
        }

        CusCcRecord.CusCcRecordBuilder cusCcRecord = CusCcRecord.builder();

        return cusCcRecord.build();
    }
}
