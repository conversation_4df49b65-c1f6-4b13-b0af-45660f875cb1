package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerChangeRecord;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerChangeRecordConvertImpl implements CustomerChangeRecordConvert {

    @Override
    public CustomerChangeRecord convert(CustomerChangeRecordForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerChangeRecord.CustomerChangeRecordBuilder customerChangeRecord = CustomerChangeRecord.builder();

        customerChangeRecord.id( form.getId() );
        customerChangeRecord.ciId( form.getCiId() );
        customerChangeRecord.infoSection( form.getInfoSection() );
        customerChangeRecord.content( form.getContent() );

        return customerChangeRecord.build();
    }
}
