<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusSourceMapper">
    <update id="updateStatusEnable">
        update cus_source set enable = '1' where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getList" resultType="com.jri.biz.cus.domain.vo.CusSourceListVO">
        select
            id, parent_id, ancestors, biz, name, sort, remark, enable, create_by, create_time, update_by, update_time
        from cus_source
        <where>
                and parent_id != 3
            <if test="query.name != null and query.name != ''">
                and name like concat('%',#{query.name},'%')
            </if>
            <if test="query.enable != null and query.enable != ''">
                and enable = #{query.enable}
            </if>
            <if test="query.remark != null and query.remark != ''">
                and remark like concat('%',#{query.remark},'%')
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and create_by like concat('%',#{query.createBy},'%')
            </if>
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by id asc
            </otherwise>
        </choose>
    </select>

    <select id="countByClue" resultType="java.lang.Long">
        select
            count(clue.id)
        from cus_customer_or_clue as clue
        left join cus_source as source on clue.source_id = source.id
        where (find_in_set(#{id}, source.ancestors) > 0 or source.id = #{id})
        and clue.is_deleted = 0
        <if test="customerFlag != null">
          <if test="customerFlag">
              and clue.type != '0'
          </if>
          <if test="!customerFlag">
              and clue.type = '0'
          </if>
        </if>
    </select>

    <select id="countByCustomerId" resultType="java.lang.Long">
        select count(source.id)
        from cus_source source
        left join cus_source_customer_introduction customer on source.id = customer.main_id
        where customer.customer_id = #{customerId}
        <if test="sourceId != null">
            and source.id <![CDATA[<>]]> #{sourceId}
        </if>
    </select>

    <select id="selectEnableChildrenById" resultType="java.lang.Long">
        select count(*) from cus_source where enable = '1' and find_in_set(#{id}, ancestors)
    </select>

</mapper>
