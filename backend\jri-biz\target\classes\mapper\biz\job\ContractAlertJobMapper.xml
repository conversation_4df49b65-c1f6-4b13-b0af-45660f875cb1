<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.job.ContractAlertJobMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.job.ContractAlertJobListVO">
        select
        contract_alert_id,
        name,
        alert_date,
        alert_status,
        contract_type,
        notification_method,
        send_time,
        message_template,
        remark,
        create_by,
        create_time,
        update_by,
        IFNULL(update_time,create_time) AS update_time,
        is_deleted
        from contract_alert_job
        <where>
            is_deleted=0
            <if test="query.name!=null and query.name!=''">
                and name like concat('%',#{query.name},'%')
            </if>
            <if test="query.alertStatus!=null and query.alertStatus!='' ">
                and alert_status= #{query.alertStatus}
            </if>
            <if test="query.contractType!=null and query.contractType!='' ">
                and contract_type= #{query.contractType}
            </if>
            <if test="query.notificationMethod!=null and query.notificationMethod!='' ">
                and notification_method like concat('%', #{query.notificationMethod}, '%')
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.entity.job.ContractAlterJob">
        select *
        from contract_alert_job
        where contract_alert_id = #{id}
    </select>

    <delete id="deleteByIds" parameterType="Long">
        update contract_alert_job
        set is_deleted ='1'
        where contract_alert_id = #{id}
    </delete>
</mapper>
