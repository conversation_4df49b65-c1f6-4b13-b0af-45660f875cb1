package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicAdministrative;
import com.jri.biz.domain.request.BasicAdministrativeForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BasicAdministrativeConvertImpl implements BasicAdministrativeConvert {

    @Override
    public BasicAdministrative convert(BasicAdministrativeForm form) {
        if ( form == null ) {
            return null;
        }

        BasicAdministrative.BasicAdministrativeBuilder basicAdministrative = BasicAdministrative.builder();

        basicAdministrative.id( form.getId() );
        basicAdministrative.parentId( form.getParentId() );
        basicAdministrative.ancestors( form.getAncestors() );
        basicAdministrative.name( form.getName() );
        basicAdministrative.sort( form.getSort() );
        basicAdministrative.remark( form.getRemark() );
        basicAdministrative.enable( form.getEnable() );

        return basicAdministrative.build();
    }
}
