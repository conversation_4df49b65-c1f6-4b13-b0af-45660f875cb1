package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcContact;
import com.jri.biz.cus.domain.request.CusCcContactForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusCcContactConvertImpl implements CusCcContactConvert {

    @Override
    public CusCcContact convert(CusCcContactForm form) {
        if ( form == null ) {
            return null;
        }

        CusCcContact.CusCcContactBuilder cusCcContact = CusCcContact.builder();

        cusCcContact.id( form.getId() );
        cusCcContact.contactName( form.getContactName() );
        cusCcContact.contactPhone( form.getContactPhone() );
        cusCcContact.post( form.getPost() );
        cusCcContact.wx( form.getWx() );
        cusCcContact.qq( form.getQq() );
        cusCcContact.sex( form.getSex() );
        cusCcContact.email( form.getEmail() );
        cusCcContact.birthday( form.getBirthday() );
        cusCcContact.source( form.getSource() );
        cusCcContact.ccId( form.getCcId() );
        cusCcContact.changeTime( form.getChangeTime() );
        cusCcContact.isLeader( form.getIsLeader() );
        cusCcContact.isOften( form.getIsOften() );

        return cusCcContact.build();
    }
}
