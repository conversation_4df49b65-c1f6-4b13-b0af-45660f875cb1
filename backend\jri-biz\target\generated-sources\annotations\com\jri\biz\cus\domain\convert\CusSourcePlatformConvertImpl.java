package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSourcePlatform;
import com.jri.biz.cus.domain.request.CusSourcePlatformForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: java<PERSON>, environment: Java 17.0.16 (Microsoft)"
)
public class CusSourcePlatformConvertImpl implements CusSourcePlatformConvert {

    @Override
    public CusSourcePlatform convert(CusSourcePlatformForm form) {
        if ( form == null ) {
            return null;
        }

        CusSourcePlatform.CusSourcePlatformBuilder cusSourcePlatform = CusSourcePlatform.builder();

        cusSourcePlatform.price( form.getPrice() );

        return cusSourcePlatform.build();
    }
}
