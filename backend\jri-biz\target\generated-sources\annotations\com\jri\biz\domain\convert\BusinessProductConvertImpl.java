package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BusinessProduct;
import com.jri.biz.domain.entity.BusinessProductActivity;
import com.jri.biz.domain.request.BusinessProductForm;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BusinessProductConvertImpl implements BusinessProductConvert {

    @Override
    public BusinessProduct convert(BusinessProductForm form) {
        if ( form == null ) {
            return null;
        }

        BusinessProduct.BusinessProductBuilder businessProduct = BusinessProduct.builder();

        businessProduct.id( form.getId() );
        businessProduct.productName( form.getProductName() );
        businessProduct.quotation( form.getQuotation() );
        businessProduct.isInContract( form.getIsInContract() );
        businessProduct.feeType( form.getFeeType() );
        businessProduct.code( form.getCode() );
        businessProduct.typeId( form.getTypeId() );
        businessProduct.sort( form.getSort() );
        businessProduct.activityStatus( form.getActivityStatus() );
        businessProduct.activityQuotation( form.getActivityQuotation() );
        businessProduct.activityDiscountTime( form.getActivityDiscountTime() );
        List<BusinessProductActivity> list = form.getActivityList();
        if ( list != null ) {
            businessProduct.activityList( new ArrayList<BusinessProductActivity>( list ) );
        }
        businessProduct.enterpriseShowFlag( form.getEnterpriseShowFlag() );
        businessProduct.enable( form.getEnable() );

        return businessProduct.build();
    }
}
