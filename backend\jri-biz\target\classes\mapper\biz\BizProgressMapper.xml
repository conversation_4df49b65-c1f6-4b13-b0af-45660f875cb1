<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.BizProgressMapper">

    <select id="listPage" resultType="com.jri.biz.domain.entity.Progress">
        select id, type, status, reason, file_name, create_by, create_time, op_type
        from biz_progress
        <where>
            <if test="query.status != null">
                and status = #{query.status}
            </if>
            <if test="query.fileName != null and query.fileName != ''">
                and file_name like concat('%',#{query.fileName},'%')
            </if>
            <if test="query.createTime != null and query.createTime != ''">
                and left(create_time, 10) = #{query.createTime}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>

