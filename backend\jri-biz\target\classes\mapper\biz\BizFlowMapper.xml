<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.BizFlowMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.BizFlowListVO">
        select id, create_by, create_time, `name`, contract_type, `type`, enable, dept_ids
        from biz_flow
        <where>
            is_deleted = 0
            and `type` = #{query.type}
            <if test="query.name != null and query.name != ''">
                and `name` like concat('%', #{query.name}, '%')
            </if>
            <if test="query.enable != null and query.enable != ''">
                and enable = #{query.enable}
            </if>
        </where>
        order by enable desc, create_time desc
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.BizFlowVO">

    </select>
</mapper>
