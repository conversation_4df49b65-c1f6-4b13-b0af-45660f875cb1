package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicBookkeepingStop;
import com.jri.biz.domain.request.BasicBookkeepingStopForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BasicBookkeepingStopConvertImpl implements BasicBookkeepingStopConvert {

    @Override
    public BasicBookkeepingStop convert(BasicBookkeepingStopForm form) {
        if ( form == null ) {
            return null;
        }

        BasicBookkeepingStop.BasicBookkeepingStopBuilder basicBookkeepingStop = BasicBookkeepingStop.builder();

        basicBookkeepingStop.id( form.getId() );
        basicBookkeepingStop.parentId( form.getParentId() );
        basicBookkeepingStop.ancestors( form.getAncestors() );
        basicBookkeepingStop.name( form.getName() );
        basicBookkeepingStop.sort( form.getSort() );
        basicBookkeepingStop.remark( form.getRemark() );
        basicBookkeepingStop.enable( form.getEnable() );

        return basicBookkeepingStop.build();
    }
}
