package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcTags;
import com.jri.biz.cus.domain.request.CusCcTagsForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusCcTagsConvertImpl implements CusCcTagsConvert {

    @Override
    public CusCcTags convert(CusCcTagsForm form) {
        if ( form == null ) {
            return null;
        }

        CusCcTags.CusCcTagsBuilder cusCcTags = CusCcTags.builder();

        return cusCcTags.build();
    }
}
