<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.UserCustomerBindRecordMapper">

    <select id="listPage" resultType="com.jri.biz.domain.vo.UserCustomerBindRecordListVO">
        select
        record.id,
        user.nick_name as user_name,
        user.phonenumber as phone,
        record.customer_id,
        info.customer_name,
        record.bind_status,
        record.update_time
        from user_customer_bind_record record
        left join sys_user user on record.user_id = user.user_id
        left join customer_information info on record.customer_id = info.customer_id
        <where>
            <if test="query.userName != null and query.userName != ''">
                and user.nick_name like concat('%', #{query.userName}, '%')
            </if>
            <if test="query.phone != null and query.phone != ''">
                and user.phonenumber like concat('%', #{query.phone}, '%')
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                and info.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.bindStatus != null and query.bindStatus != ''">
                and record.bind_status = #{query.bindStatus}
            </if>
        </where>
        order by record.update_time desc
    </select>

    <update id="batchUnbind">
        update user_customer_bind_record
        set bind_status = #{bindStatus}, update_time = now()
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
