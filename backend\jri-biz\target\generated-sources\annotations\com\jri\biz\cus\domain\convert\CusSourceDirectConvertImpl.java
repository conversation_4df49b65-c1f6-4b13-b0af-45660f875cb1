package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSourceDirect;
import com.jri.biz.cus.domain.request.CusSourceDirectForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: java<PERSON>, environment: Java 17.0.16 (Microsoft)"
)
public class CusSourceDirectConvertImpl implements CusSourceDirectConvert {

    @Override
    public CusSourceDirect convert(CusSourceDirectForm form) {
        if ( form == null ) {
            return null;
        }

        CusSourceDirect.CusSourceDirectBuilder cusSourceDirect = CusSourceDirect.builder();

        cusSourceDirect.id( form.getId() );
        cusSourceDirect.mainId( form.getMainId() );
        cusSourceDirect.amount( form.getAmount() );
        cusSourceDirect.chargeTime( form.getChargeTime() );

        return cusSourceDirect.build();
    }
}
