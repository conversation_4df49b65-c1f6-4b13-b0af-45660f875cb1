<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.ClueAnalyseMapper">

    <select id="baseSourcePage" resultType="com.jri.biz.cus.domain.vo.BaseClueSourceAnalyseVO">
        select
            source.id,
            source.name
        from cus_source source
        <where>
            and source.parent_id != 0
            <choose>
                <when test="query.biz == '其他'">
                    and source.biz is null
                </when>
                <otherwise>
                    and source.biz = #{query.biz}
                </otherwise>
            </choose>

            <if test="query.name != null and query.name != ''">
                and source.name like concat('%',#{query.name},'%')
            </if>
        </where>
    </select>

    <select id="CustomerIntroductionSourcePage"
            resultType="com.jri.biz.cus.domain.vo.ClueSourceCustomerIntroductionAnalyseVO">
        select
            information.customer_id as id,
            information.customer_name as name
        from cus_customer_or_clue clue
        left join customer_information information on clue.introduction_customer_id = information.customer_id
        <where>
                and clue.is_deleted = 0 and information.customer_id is not null
            <if test="query.name != null and query.name != ''">
                and information.customer_name like concat('%',#{query.name},'%')
            </if>
        </where>
        group by information.customer_id, information.customer_name
    </select>

    <select id="clueCount" resultType="java.lang.Long">
        select
            count(clue.id)
        from cus_customer_or_clue clue
        left join cus_source source on clue.source_id = source.id
        <where>
                and clue.is_deleted = 0
            <if test="isSea != null and isSea != ''">
                and clue.is_sea = #{isSea}
            </if>
            <if test="sourceId != null">
                and (find_in_set(#{sourceId}, source.ancestors) > 0 or source.id = #{sourceId})
            </if>
            <if test="startTime != null and endTime != null">
                and clue.create_time <![CDATA[>=]]> #{startTime} and clue.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="userId != null">
                and clue.current_user_id = #{userId}
            </if>
            <if test="companyFlag != null and companyFlag">
                and clue.customer_id is not null
            </if>
            <if test="biz != null and biz != ''">
                <choose>
                    <when test="biz == '其他'">
                        and source.biz is null
                    </when>
                    <otherwise>
                        and source.biz = #{biz}
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="getGMV" resultType="java.math.BigDecimal">
        select
            COALESCE(sum(${actual_amount_column}), 0)
        from cus_customer_or_clue clue
        left join cus_source source on clue.source_id = source.id
        left join cus_cc_business business on clue.id = business.cc_id and business.id = (select min(id) from cus_cc_business where cc_id = clue.id and (stage = '赢单' or stage = '已收定金,待打尾款') and is_deleted = 0)
        <where>
                and clue.is_deleted = 0
            <if test="isSea != null and isSea != ''">
                and clue.is_sea = #{isSea}
            </if>
            <if test="sourceId != null">
                and (find_in_set(#{sourceId}, source.ancestors) > 0 or source.id = #{sourceId})
            </if>
            <if test="startTime != null and endTime != null">
                and clue.create_time <![CDATA[>=]]> #{startTime} and clue.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="userId != null">
                and clue.current_user_id = #{userId}
            </if>
            <if test="biz != null and biz != ''">
                <choose>
                    <when test="biz == '其他'">
                        and source.biz is null
                    </when>
                    <otherwise>
                        and source.biz = #{biz}
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="customerIntroductionClueCount" resultType="java.lang.Long">
        select
            count(clue.id)
        from cus_customer_or_clue clue
        where clue.is_deleted = 0 and clue.introduction_customer_id = #{customerId}
        <if test="startTime != null and endTime != null">
            and clue.create_time <![CDATA[>=]]> #{startTime} and clue.create_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="companyFlag != null and companyFlag">
            and clue.customer_id is not null
        </if>
    </select>

    <select id="getCustomerIntroductionGMV" resultType="java.math.BigDecimal">
        select
        COALESCE(sum(${actual_amount_column}), 0)
        from cus_customer_or_clue clue
        left join cus_cc_business business on clue.id = business.cc_id and business.id = (select min(id) from cus_cc_business where cc_id = clue.id and (stage = '赢单' or stage = '已收定金,待打尾款') and is_deleted = 0)
        <where>
            and clue.is_deleted = 0 and clue.introduction_customer_id = #{customerId}
            <if test="startTime != null and endTime != null">
                and clue.create_time <![CDATA[>=]]> #{startTime} and clue.create_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>

    <select id="customerIntroductionCount" resultType="java.lang.Long">
        select
            count(clue.id)
        from cus_customer_or_clue clue
        where clue.is_deleted = 0 and clue.introduction_customer_id = #{customerId}
    </select>

    <select id="countClueBySourceId" resultType="java.lang.Long">
        select
            count(id)
        from cus_customer_or_clue
        where is_deleted = 0
          and source_id = #{sourceId}
        <if test="isSea != null and isSea != ''">
            and is_sea = #{isSea}
        </if>
        <if test="startTime != null">
            and create_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and create_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>

    <select id="baseStaffSourcePage" resultType="com.jri.biz.cus.domain.vo.BaseStaffSourceAnalyseVO">
        select
            user.user_id as id,
            user.nick_name as name,
            dept.dept_name
        from sys_user as user
        left join sys_dept as dept on user.dept_id = dept.dept_id
        where user.del_flag = '0' and user.status = '0' and user.enterprise_flag = 0
        <if test="query.userName != null and query.userName != ''">
            and user.nick_name like concat('%', #{query.userName}, '%')
        </if>
        <if test="query.deptId != null">
            and (FIND_IN_SET(#{query.deptId},dept.ancestors) > 0 or #{query.deptId} = dept.dept_id)
        </if>
    </select>

    <select id="getClueList" resultType="com.jri.biz.cus.domain.entity.CusCustomerOrClue">
        select clue.*
        from cus_customer_or_clue clue
        left join cus_source source on source.id = clue.source_id
        where clue.is_deleted = 0
        <if test="isSea != null and isSea != ''">
            and clue.is_sea = #{isSea}
        </if>
        <if test="sourceId != null">
            and source.id = #{sourceId}
        </if>
        <if test="userId != null">
            and clue.current_user_id = #{userId}
        </if>
        <if test="sourceBiz != null and sourceBiz != ''">
            and source.biz = #{sourceBiz}
        </if>
        <if test="startTime != null and endTime != null">
            and clue.create_time <![CDATA[>=]]> #{startTime} and clue.create_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>

    <select id="selectClueDealDays" resultType="java.lang.Long">
        SELECT
            sum((if(DATEDIFF(business.win_time, COALESCE(clue.get_time, clue.create_time)) <![CDATA[<]]> 0, 0, DATEDIFF(business.win_time, COALESCE(clue.get_time, clue.create_time))) + 1))
        FROM cus_customer_or_clue clue
        left join cus_cc_business business on clue.id = business.cc_id and business.id = (select min(id) from cus_cc_business where cc_id = clue.id)
        where business.win_time is not null and clue.is_deleted = 0 and clue.is_sea = '0'
        <if test="startTime != null and endTime != null">
            and clue.create_time <![CDATA[>=]]> #{startTime} and clue.create_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="userId != null">
            and clue.current_user_id = #{userId}
        </if>
    </select>

    <select id="platformAppealCount" resultType="java.lang.Long">
        select
            count(record.id)
        from clue_appeal_record record
        left join cus_customer_or_clue clue on record.clue_id = clue.id
        where clue.is_deleted = 0
        <if test="isSea != null and isSea != ''">
            and clue.is_sea = #{isSea}
        </if>
        <if test="sourceId != null">
            and clue.source_id = #{sourceId}
        </if>
        <if test="startTime != null and endTime != null">
            and clue.create_time <![CDATA[>=]]> #{startTime} and clue.create_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="userId != null">
            and clue.current_user_id = #{userId}
        </if>
        <if test="appealStatus != null and appealStatus != ''">
            and record.status = #{appealStatus}
        </if>

    </select>


</mapper>
