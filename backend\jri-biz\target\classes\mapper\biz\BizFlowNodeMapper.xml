<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.BizFlowNodeMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.BizFlowNodeListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.BizFlowNodeVO">

    </select>
    <select id="getListByMainId" resultType="com.jri.biz.domain.entity.BizFlowNode">
        select bfn.*, su.nick_name
        from biz_flow_node bfn left join sys_user su on bfn.user_id = su.user_id
        where bfn.main_id = #{id}
    </select>
</mapper>
