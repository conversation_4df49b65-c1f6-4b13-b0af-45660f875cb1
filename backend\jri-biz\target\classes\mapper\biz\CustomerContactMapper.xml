<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerContactMapper">
    <delete id="deleteById">
        update customer_tax_export_detail set is_deleted=1,update_by =#{updateBy} where id =#{id}
    </delete>


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerContactListVO">
        select * from customer_contact where ci_id = #{query.ciId} and is_deleted = 0
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerContactVO">
        select * from customer_contact where is_deleted=0 and id= #{id}
    </select>
    <select id="getDetailByCiId" resultType="com.jri.biz.domain.vo.CustomerContactVO">
        select * from customer_contact where ci_id = #{ciId} and is_deleted = 0
    </select>

    <delete id="removeByCiId">
        delete from customer_contact where ci_id = #{ciId}
    </delete>



    <!--添加订单到order-->
    <insert id="add" parameterType="com.jri.biz.domain.entity.CustomerContact" >
        <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
            select LAST_INSERT_ID()
        </selectKey>
        insert into customer_contact(
        <if test="ciId != null and ciId != '' ">ci_id,</if>
        <if test="name != null and name != '' ">name,</if>
        <if test="phone != null and phone != '' ">phone,</if>
        <if test="dept != null and dept != '' ">dept,</if>
        <if test="post != null and post != ''">post,</if>
        <if test="wechat != null and wechat != ''">wechat,</if>
        <if test="details != null and details != ''">details,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="isLeader != null and isLeader != ''">is_leader,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="ciId != null and ciId != ''">#{ciId},</if>
        <if test="name != null and name != ''">#{name},</if>
        <if test="phone != null and phone != ''">#{phone},</if>
        <if test="dept != null and dept != ''">#{dept},</if>
        <if test="post != null and post != ''">#{post},</if>
        <if test="wechat != null and wechat != ''">#{wechat},</if>
        <if test="details != null and details != ''">#{details},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="isLeader != null and isLeader != ''">#{isLeader},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>
</mapper>
