<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.ContractTempMapper">
    <update id="updateStatus">
        update contract_temp set status = #{form.reviewStatus} where id = #{form.mainId}
    </update>


    <select id="listPage" resultType="com.jri.biz.domain.vo.ContractTempListVO">
        select ct.*, su.nick_name createBy
        from contract_temp ct
        left join sys_user su on ct.create_by = su.user_id
        <where>
            ct.is_deleted = 0 and ct.status = '1'
            <if test="query.tempName != null and query.tempName != ''">
                and ct.temp_name like concat('%', #{query.tempName}, '%')
            </if>
            <if test="query.contractType != null and query.contractType != ''">
                and ct.contract_type = #{query.contractType}
            </if>
            <if test="query.enable != null and query.enable != ''">
                and ct.enable = #{query.enable}
            </if>
        </where>
        order by ct.create_time desc
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.ContractTempVO">
        select ct.*, su.nick_name createBy
        from contract_temp ct
                 left join sys_user su on ct.create_by = su.user_id
        where ct.id = #{id}
    </select>
    <select id="listMyCreate" resultType="com.jri.biz.domain.vo.ContractTempListVO">
        select ct.*, su.nick_name createBy
        from contract_temp ct
        left join sys_user su on ct.create_by = su.user_id
        <where>
            and ct.create_by = #{query.userId}
            <if test="query.tempName != null and query.tempName != ''">
                and ct.temp_name like concat('%', #{query.tempName}, '%')
            </if>
            <if test="query.contractType != null and query.contractType != ''">
                and ct.contract_type = #{query.contractType}
            </if>
            <if test="query.status != null and query.status != ''">
                and ct.status = #{query.status}
            </if>
        </where>
        order by ct.create_time desc
    </select>
    <select id="listMyAudit" resultType="com.jri.biz.domain.vo.ContractTempListVO">
       select ct.id,
                ct.temp_name,
                ct.contract_type,
                ct.remark,
                ct.enable,
                ct.create_time,
                ct.update_by,
                ct.update_time,
                su.nick_name createBy,
                bnh.review_status status
        from contract_temp ct
        left join sys_user su on ct.create_by = su.user_id
        left join (
        select max(bnh.node_key) node_key,ct.id from contract_temp ct left join biz_node_history bnh ON
        bnh.main_id = ct.id
        and bnh.type = '0'
        where bnh.user_id = #{query.userId}
        group by ct.id
        )mm on ct.id = mm.id
        left join biz_node_history bnh on mm.id = bnh.main_id and mm.node_key = bnh.node_key
        <where>
            and bnh.user_id = #{query.userId}
            <if test="query.tempName != null and query.tempName != ''">
                and ct.temp_name like concat('%', #{query.tempName}, '%')
            </if>
            <if test="query.contractType != null and query.contractType != ''">
                and ct.contract_type = #{query.contractType}
            </if>
            <if test="query.status != null and query.status != ''">
                and bnh.review_status = #{query.status}
            </if>
        </where>
        order by ct.create_time desc
    </select>
</mapper>
