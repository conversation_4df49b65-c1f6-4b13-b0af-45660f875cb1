<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusCcContactMapper">


    <select id="listPage" resultType="com.jri.biz.cus.domain.vo.CusCcContactListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.cus.domain.vo.CusCcContactVO">

    </select>
    <select id="listByCcId" resultType="com.jri.biz.cus.domain.vo.CusCcContactListVO">
        select *
        from cus_cc_contact
        where is_deleted = 0 and cc_id = #{ccId}
        order by create_time
    </select>
    <select id="getListNotMain" resultType="com.jri.biz.cus.domain.entity.CusCcContact">
        select *
        from cus_cc_contact
        where is_deleted = 0 and cc_id = #{id} and id != #{mainContactId}
        order by create_time
    </select>
</mapper>
