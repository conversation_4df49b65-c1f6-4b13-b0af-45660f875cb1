package com.jri.biz.domain.convert.address;

import com.jri.biz.domain.entity.address.AddressSupplier;
import com.jri.biz.domain.request.address.AddressSupplierForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class AddressSupplierConvertImpl implements AddressSupplierConvert {

    @Override
    public AddressSupplier convert(AddressSupplierForm form) {
        if ( form == null ) {
            return null;
        }

        AddressSupplier.AddressSupplierBuilder addressSupplier = AddressSupplier.builder();

        addressSupplier.id( form.getId() );
        addressSupplier.supplier( form.getSupplier() );
        addressSupplier.addressCost( form.getAddressCost() );
        addressSupplier.phone( form.getPhone() );
        addressSupplier.bankAccount( form.getBankAccount() );
        addressSupplier.feeType( form.getFeeType() );
        addressSupplier.validTo( form.getValidTo() );
        addressSupplier.remark( form.getRemark() );
        addressSupplier.enable( form.getEnable() );

        return addressSupplier.build();
    }
}
