package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerBankCommon;
import com.jri.biz.domain.request.CustomerBankCommonForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerBankCommonConvertImpl implements CustomerBankCommonConvert {

    @Override
    public CustomerBankCommon convert(CustomerBankCommonForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerBankCommon.CustomerBankCommonBuilder customerBankCommon = CustomerBankCommon.builder();

        customerBankCommon.id( form.getId() );
        customerBankCommon.bankId( form.getBankId() );
        customerBankCommon.commonBankName( form.getCommonBankName() );
        customerBankCommon.commonBankAccount( form.getCommonBankAccount() );
        customerBankCommon.commonInternetbankFlag( form.getCommonInternetbankFlag() );
        customerBankCommon.commonInternetbankAccount( form.getCommonInternetbankAccount() );
        customerBankCommon.commonReceiptCardPassword( form.getCommonReceiptCardPassword() );
        customerBankCommon.commonInternetbankType( form.getCommonInternetbankType() );
        customerBankCommon.commonReceiptCardFlag( form.getCommonReceiptCardFlag() );

        return customerBankCommon.build();
    }
}
