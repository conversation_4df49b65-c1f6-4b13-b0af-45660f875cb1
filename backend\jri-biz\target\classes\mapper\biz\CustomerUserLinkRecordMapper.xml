<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerUserLinkRecordMapper">

    <select id="getByLinkRecord" resultType="com.jri.biz.domain.entity.CustomerUserLinkRecord">
        select *
        from customer_user_link_record
        where customer_id = #{customerId}
        and user_id = #{userId}
        and role = #{role}
        and unlink_time is null
    </select>

    <select id="linkRecordCount" resultType="java.lang.Long">
        select count(DISTINCT customer_id)
        from customer_user_link_record
        left join sys_user on customer_user_link_record.user_id = sys_user.user_id
        left join sys_dept on sys_user.dept_id = sys_dept.dept_id
        where (
                (#{endDateStart} between link_time and unlink_time) or
                (link_time &lt;= #{endDateStart} and unlink_time is null)
            )
        <if test="userId != null">
            and customer_user_link_record.user_id = #{userId}
        </if>
        <if test="customerSuccess != null and customerSuccess != ''">
            and sys_user.nick_name like concat('%',#{customerSuccess}, '%')
        </if>
        <if test="deptId != null and deptId != ''">
            and (FIND_IN_SET(#{deptId},sys_dept.ancestors)>0 or
            #{deptId}=sys_dept.dept_id)
        </if>
        <if test="role != null and role !=''">
            and customer_user_link_record.role = #{role}
        </if>
    </select>
</mapper>
