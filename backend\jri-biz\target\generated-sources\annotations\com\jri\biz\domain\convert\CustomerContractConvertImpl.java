package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerContract;
import com.jri.biz.domain.request.CustomerContractForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CustomerContractConvertImpl implements CustomerContractConvert {

    @Override
    public CustomerContract convert(CustomerContractForm form) {
        if ( form == null ) {
            return null;
        }

        CustomerContract.CustomerContractBuilder customerContract = CustomerContract.builder();

        customerContract.contractId( form.getContractId() );
        customerContract.ciId( form.getCiId() );
        customerContract.contractName( form.getContractName() );
        customerContract.contractType( form.getContractType() );
        customerContract.contractNo( form.getContractNo() );
        customerContract.contactPerson( form.getContactPerson() );
        customerContract.contactPhone( form.getContactPhone() );
        customerContract.type( form.getType() );
        customerContract.taxpayerType( form.getTaxpayerType() );
        customerContract.startTime( form.getStartTime() );
        customerContract.endTime( form.getEndTime() );
        customerContract.monthNum( form.getMonthNum() );
        customerContract.salesRevenue( form.getSalesRevenue() );
        customerContract.remark( form.getRemark() );
        customerContract.serviceCost( form.getServiceCost() );
        customerContract.otherCost( form.getOtherCost() );
        customerContract.totalCostCn( form.getTotalCostCn() );
        customerContract.totalCost( form.getTotalCost() );
        customerContract.productionCost( form.getProductionCost() );
        customerContract.productId( form.getProductId() );
        customerContract.otherRemark( form.getOtherRemark() );
        customerContract.originId( form.getOriginId() );
        customerContract.bizType( form.getBizType() );
        customerContract.identityNumber( form.getIdentityNumber() );
        customerContract.companyName( form.getCompanyName() );
        customerContract.legalPerson( form.getLegalPerson() );
        customerContract.legalPhone( form.getLegalPhone() );
        customerContract.custodyAddress( form.getCustodyAddress() );
        customerContract.changeReason( form.getChangeReason() );
        customerContract.htmlStr( form.getHtmlStr() );
        customerContract.tempId( form.getTempId() );
        customerContract.companyPerson( form.getCompanyPerson() );
        customerContract.companyPhone( form.getCompanyPhone() );
        customerContract.companyAddress( form.getCompanyAddress() );
        customerContract.isEstablish( form.getIsEstablish() );
        customerContract.softwareFee( form.getSoftwareFee() );
        customerContract.everyYear( form.getEveryYear() );
        customerContract.contactAddress( form.getContactAddress() );
        customerContract.accountNumber( form.getAccountNumber() );
        customerContract.activityId( form.getActivityId() );
        customerContract.activityQuotation( form.getActivityQuotation() );
        customerContract.activityDiscountTime( form.getActivityDiscountTime() );
        customerContract.activityRemark( form.getActivityRemark() );
        customerContract.activityTxt( form.getActivityTxt() );
        customerContract.payrollService( form.getPayrollService() );
        customerContract.branchOffice( form.getBranchOffice() );
        customerContract.priceChangeFlag( form.getPriceChangeFlag() );
        customerContract.discount( form.getDiscount() );
        customerContract.discountAmount( form.getDiscountAmount() );
        customerContract.discountTime( form.getDiscountTime() );
        customerContract.discountRate( form.getDiscountRate() );
        customerContract.discountTimeRate( form.getDiscountTimeRate() );
        customerContract.changeStartTime( form.getChangeStartTime() );

        return customerContract.build();
    }
}
