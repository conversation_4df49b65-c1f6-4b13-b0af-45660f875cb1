<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.OrderMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.OrderListVO">
        select o.*, ci.customer_name, ci.customer_no, ot.type_name orderTypeName, su.nick_name executorName, su2.nick_name createBy
        from order_info o
                 left join customer_information ci on o.ci_id = ci.customer_id
                 left join order_type ot on o.order_type_id = ot.id
                 left join sys_user su on o.executor = su.user_id
                 left join sys_user su2 on o.create_by = su2.user_id
        <where>
            o.is_deleted = 0
            and o.create_by = #{query.userId}
            and o.order_status != '3'
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerNo != null and query.customerNo != ''">
                and ci.customer_no like concat('%', #{query.customerNo}, '%')
            </if>
            <if test="query.orderTitle != null and query.orderTitle != ''">
                and o.order_title like concat('%', #{query.orderTitle}, '%')
            </if>
            <if test="query.orderNo != null and query.orderNo != ''">
                and o.order_no like concat('%', #{query.orderNo}, '%')
            </if>
            <if test="query.isUrgent != null and query.isUrgent != ''">
                and o.is_urgent = #{query.isUrgent}
            </if>
            <if test="query.orderStatus != null and query.orderStatus != ''">
                and o.order_status = #{query.orderStatus}
            </if>
            <if test="query.orderTypeId != null">
                and (find_in_set(#{query.orderTypeId}, ot.ancestors) or o.order_type_id = #{query.orderTypeId})
            </if>
            <if test="query.executor != null and query.executor != ''">
                and o.executor = #{query.executor}
            </if>
            <if test="query.noExecutorFlag != null and query.noExecutorFlag">
                and o.executor is null
            </if>
            <if test="query.timeoutFlag != null and query.timeoutFlag">
                and ((date_format(o.expect_time,'%y%m%d') &lt; date_format(now(),'%y%m%d') and o.order_status = '0') or
                (o.order_status = '1' and date_format(o.expect_time,'%y%m%d') &lt; date_format(o.complete_time,'%y%m%d')))
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and su2.nick_name like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and o.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.completeTimeStart != null and query.completeTimeEnd != null">
                and o.complete_time between #{query.completeTimeStart} and #{query.completeTimeEnd}
            </if>
            <choose>
                <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                    order by o.${query.orderByColumn}
                    <if test="query.isAsc!=null and query.isAsc!=''">
                        ${query.isAsc}
                    </if>
                </when>
                <when test='query.orderByColumn == "no_limit"'>

                </when>
                <otherwise>
                    order by o.is_urgent desc, o.create_time desc
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.OrderVO">
        select o.*, ci.customer_name, ot.type_name orderTypeName, ot.supplement_explain supplementExplain, ba.name administrative_name
        from order_info o
        left join customer_information ci on o.ci_id = ci.customer_id
        left join order_type ot on o.order_type_id = ot.id
        left join basic_administrative ba on o.administrative_id = ba.id
        where o.id = #{id}
    </select>
    <select id="listMyToDo" resultType="com.jri.biz.domain.vo.OrderListVO">
        select o.*, ci.customer_name, ci.customer_no, ot.type_name orderTypeName, su.nick_name executorName, su2.nick_name createBy
        from order_info o
        left join customer_information ci on o.ci_id = ci.customer_id
        left join order_type ot on o.order_type_id = ot.id
        left join sys_user su on o.executor = su.user_id
        left join sys_user su2 on o.create_by = su2.user_id
        <where>
            o.is_deleted = 0
            and o.executor = #{query.userId}
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerNo != null and query.customerNo != ''">
                and ci.customer_no like concat('%', #{query.customerNo}, '%')
            </if>
            <if test="query.orderTitle != null and query.orderTitle != ''">
                and o.order_title like concat('%', #{query.orderTitle}, '%')
            </if>
            <if test="query.orderNo != null and query.orderNo != ''">
                and o.order_no like concat('%', #{query.orderNo}, '%')
            </if>
            <if test="query.isUrgent != null and query.isUrgent != ''">
                and o.is_urgent = #{query.isUrgent}
            </if>
            <if test="query.orderStatus != null and query.orderStatus != ''">
                and o.order_status = #{query.orderStatus}
            </if>
            <if test="query.orderTypeId != null">
                and (find_in_set(#{query.orderTypeId}, ot.ancestors) or o.order_type_id = #{query.orderTypeId})
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and (su.nick_name like concat('%', #{query.keyword}, '%') or
                    ot.type_name like concat('%', #{query.keyword}, '%') or
                    ci.customer_name like concat('%', #{query.keyword}, '%')
                )
            </if>
            <if test="query.expectTime != null">
                and o.expect_time = #{query.expectTime}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and su2.nick_name like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.timeoutFlag != null and query.timeoutFlag">
                and ((date_format(o.expect_time,'%y%m%d') &lt; date_format(now(),'%y%m%d') and o.order_status = '0') or
                (o.order_status = '1' and date_format(o.expect_time,'%y%m%d') &lt; date_format(o.complete_time,'%y%m%d')))
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and o.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.completeTimeStart != null and query.completeTimeEnd != null">
                and o.complete_time between #{query.completeTimeStart} and #{query.completeTimeEnd}
            </if>
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by o.${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <when test='query.orderByColumn == "no_limit"'>

            </when>
            <otherwise>
                order by o.is_urgent desc, o.create_time desc
            </otherwise>
        </choose>

    </select>
    <select id="listMyAssign" resultType="com.jri.biz.domain.vo.OrderListVO">
        select o.*, ci.customer_name, ci.customer_no, ot.type_name orderTypeName, su.nick_name executorName, su2.nick_name createBy
        from order_info o
        left join customer_information ci on o.ci_id = ci.customer_id
        left join order_type ot on o.order_type_id = ot.id
        left join sys_user su on o.executor = su.user_id
        left join sys_user su2 on o.create_by = su2.user_id
        left join order_record orec on o.id = orec.order_id and orec.record_name = '指派'
        <where>
            o.is_deleted = 0
            and orec.create_by = #{query.userId}
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerNo != null and query.customerNo != ''">
                and ci.customer_no like concat('%', #{query.customerNo}, '%')
            </if>
            <if test="query.orderTitle != null and query.orderTitle != ''">
                and o.order_title like concat('%', #{query.orderTitle}, '%')
            </if>
            <if test="query.orderNo != null and query.orderNo != ''">
                and o.order_no like concat('%', #{query.orderNo}, '%')
            </if>
            <if test="query.isUrgent != null and query.isUrgent != ''">
                and o.is_urgent = #{query.isUrgent}
            </if>
            <if test="query.orderStatus != null and query.orderStatus != ''">
                and o.order_status = #{query.orderStatus}
            </if>
            <if test="query.orderTypeId != null">
                and (find_in_set(#{query.orderTypeId}, ot.ancestors) or o.order_type_id = #{query.orderTypeId})
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and su2.nick_name like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and o.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.completeTimeStart != null and query.completeTimeEnd != null">
                and o.complete_time between #{query.completeTimeStart} and #{query.completeTimeEnd}
            </if>
        </where>
        group by o.id
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by o.${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <when test='query.orderByColumn == "no_limit"'>

            </when>
            <otherwise>
                order by o.is_urgent desc, o.create_time desc
            </otherwise>
        </choose>
    </select>
    <select id="listAll" resultType="com.jri.biz.domain.vo.OrderListVO">
        select o.*, ci.customer_name, ci.customer_no, ot.type_name orderTypeName, su1.nick_name executorName, su.nick_name createBy
        from order_info o
        left join customer_information ci on o.ci_id = ci.customer_id
        left join order_type ot on o.order_type_id = ot.id
        left join sys_user su1 on o.executor = su1.user_id
        left join sys_user su on o.create_by = su.user_id
        left join sys_dept d on su.dept_id = d.dept_id
        <where>
            o.is_deleted = 0
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerNo != null and query.customerNo != ''">
                and ci.customer_no like concat('%', #{query.customerNo}, '%')
            </if>
            <if test="query.orderTitle != null and query.orderTitle != ''">
                and o.order_title like concat('%', #{query.orderTitle}, '%')
            </if>
            <if test="query.orderNo != null and query.orderNo != ''">
                and o.order_no like concat('%', #{query.orderNo}, '%')
            </if>
            <if test="query.isUrgent != null and query.isUrgent != ''">
                and o.is_urgent = #{query.isUrgent}
            </if>
            <if test="query.orderStatus != null and query.orderStatus != ''">
                and o.order_status = #{query.orderStatus}
            </if>
            <if test="query.orderTypeId != null">
                and (find_in_set(#{query.orderTypeId}, ot.ancestors) or o.order_type_id = #{query.orderTypeId})
            </if>
            <if test="query.executor != null and query.executor != ''">
                and o.executor = #{query.executor}
            </if>
            <if test="query.noExecutorFlag != null and query.noExecutorFlag">
                and o.executor is null
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and su.nick_name like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.timeoutFlag != null and query.timeoutFlag">
                and ((date_format(o.expect_time,'%y%m%d') &lt; date_format(now(),'%y%m%d') and o.order_status = '0') or
                (o.order_status = '1' and date_format(o.expect_time,'%y%m%d') &lt; date_format(o.complete_time,'%y%m%d')))
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and o.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.completeTimeStart != null and query.completeTimeEnd != null">
                and o.complete_time between #{query.completeTimeStart} and #{query.completeTimeEnd}
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${query.params.dataScope}
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by o.${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <when test='query.orderByColumn == "no_limit"'>

            </when>
            <otherwise>
                order by o.is_urgent desc, o.create_time desc
            </otherwise>
        </choose>
    </select>
    <select id="listAbnormal" resultType="com.jri.biz.domain.vo.OrderListVO">
        select o.*, ci.customer_name, ci.customer_no, ot.type_name orderTypeName, su1.nick_name executorName, su.nick_name createBy
        from order_info o
        left join customer_information ci on o.ci_id = ci.customer_id
        left join order_type ot on o.order_type_id = ot.id
        left join sys_user su1 on o.executor = su1.user_id
        left join sys_user su on o.create_by = su.user_id
        left join sys_dept d on su.dept_id = d.dept_id
        <where>
            o.is_deleted = 0
            and o.order_status = '3'
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerNo != null and query.customerNo != ''">
                and ci.customer_no like concat('%', #{query.customerNo}, '%')
            </if>
            <if test="query.orderTitle != null and query.orderTitle != ''">
                and o.order_title like concat('%', #{query.orderTitle}, '%')
            </if>
            <if test="query.orderNo != null and query.orderNo != ''">
                and o.order_no like concat('%', #{query.orderNo}, '%')
            </if>
            <if test="query.isUrgent != null and query.isUrgent != ''">
                and o.is_urgent = #{query.isUrgent}
            </if>
            <if test="query.orderStatus != 3">
                and o.order_status = '99'
            </if>
            <if test="query.orderTypeId != null">
                and (find_in_set(#{query.orderTypeId}, ot.ancestors) or o.order_type_id = #{query.orderTypeId})
            </if>
            <if test="query.executor != null and query.executor != ''">
                and o.executor = #{query.executor}
            </if>
            <if test="query.noExecutorFlag != null and query.noExecutorFlag">
                and o.executor is null
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and su.nick_name like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and o.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.completeTimeStart != null and query.completeTimeEnd != null">
                and o.complete_time between #{query.completeTimeStart} and #{query.completeTimeEnd}
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${query.params.dataScope}
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by o.${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <when test='query.orderByColumn == "no_limit"'>

            </when>
            <otherwise>
                order by o.is_urgent desc, o.create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="getList" resultType="com.jri.biz.domain.vo.OrderListVO">
        select o.*, ci.customer_name, ci.customer_no, ot.type_name orderTypeName, su1.nick_name executorName, su.nick_name createBy
        from order_info o
        left join customer_information ci on o.ci_id = ci.customer_id
        left join order_type ot on o.order_type_id = ot.id
        left join sys_user su1 on o.executor = su1.user_id
        left join sys_user su on o.create_by = su.user_id
        left join sys_dept d on su.dept_id = d.dept_id
        <where>
            and o.is_deleted = 0
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerNo != null and query.customerNo != ''">
                and ci.customer_no like concat('%', #{query.customerNo}, '%')
            </if>
            <if test="query.orderTitle != null and query.orderTitle != ''">
                and o.order_title like concat('%', #{query.orderTitle}, '%')
            </if>
            <if test="query.orderNo != null and query.orderNo != ''">
                and o.order_no like concat('%', #{query.orderNo}, '%')
            </if>
            <if test="query.isUrgent != null and query.isUrgent != ''">
                and o.is_urgent = #{query.isUrgent}
            </if>
            <choose>
                <when test="query.orderStatusList != null and query.orderStatusList.size() > 0">
                    and o.order_status in
                    <foreach collection="query.orderStatusList" item="orderStatus" open="(" separator="," close=")">
                        #{orderStatus}
                    </foreach>
                </when>
                <otherwise>
                    and o.order_status = '99'
                </otherwise>
            </choose>

            <if test="query.orderTypeId != null">
                and (find_in_set(#{query.orderTypeId}, ot.ancestors) or o.order_type_id = #{query.orderTypeId})
            </if>
            <if test="query.userId != null">
                and o.create_by = #{query.userId}
            </if>
            <if test="query.executor != null and query.executor != ''">
                and o.executor = #{query.executor}
            </if>
            <if test="query.noExecutorFlag != null and query.noExecutorFlag">
                and o.executor is null
            </if>
            <if test="query.timeoutFlag != null and query.timeoutFlag">
                and ((date_format(o.expect_time,'%y%m%d') &lt; date_format(now(),'%y%m%d') and o.order_status = '0') or
                (o.order_status = '1' and date_format(o.expect_time,'%y%m%d') &lt; date_format(o.complete_time,'%y%m%d')))
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and su.nick_name like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and o.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.completeTimeStart != null and query.completeTimeEnd != null">
                and o.complete_time between #{query.completeTimeStart} and #{query.completeTimeEnd}
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${query.params.dataScope}
        order by o.is_urgent desc, o.create_time desc
    </select>
</mapper>
