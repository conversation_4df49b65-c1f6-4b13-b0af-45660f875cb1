<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.OrderRecordMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.OrderRecordListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.OrderRecordVO">

    </select>
    <select id="getListByOrderId" resultType="com.jri.biz.domain.vo.OrderRecordListVO">
        select rec.*, su.nick_name executorName, su1.nick_name createBy
        from order_record rec
        left join sys_user su on rec.executor = su.user_id
        left join sys_user su1 on rec.create_by = su1.user_id
        where rec.order_id = #{orderId}
        order by rec.create_time
    </select>
</mapper>
