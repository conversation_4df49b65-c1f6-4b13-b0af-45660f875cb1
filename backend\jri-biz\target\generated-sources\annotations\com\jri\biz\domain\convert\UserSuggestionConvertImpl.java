package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.UserSuggestion;
import com.jri.biz.domain.request.suggestion.UserSuggestionAddForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class UserSuggestionConvertImpl implements UserSuggestionConvert {

    @Override
    public UserSuggestion convert(UserSuggestionAddForm form) {
        if ( form == null ) {
            return null;
        }

        UserSuggestion.UserSuggestionBuilder userSuggestion = UserSuggestion.builder();

        userSuggestion.customerId( form.getCustomerId() );
        userSuggestion.suggestionData( form.getSuggestionData() );

        return userSuggestion.build();
    }
}
