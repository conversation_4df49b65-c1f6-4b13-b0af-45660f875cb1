package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.ContractTemp;
import com.jri.biz.domain.request.ContractTempForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class ContractTempConvertImpl implements ContractTempConvert {

    @Override
    public ContractTemp convert(ContractTempForm form) {
        if ( form == null ) {
            return null;
        }

        ContractTemp.ContractTempBuilder contractTemp = ContractTemp.builder();

        contractTemp.id( form.getId() );
        contractTemp.tempName( form.getTempName() );
        contractTemp.contractType( form.getContractType() );
        contractTemp.flowId( form.getFlowId() );
        contractTemp.remark( form.getRemark() );
        contractTemp.htmlStr( form.getHtmlStr() );
        contractTemp.fieldList( form.getFieldList() );
        contractTemp.status( form.getStatus() );
        contractTemp.enable( form.getEnable() );
        contractTemp.urls( form.getUrls() );

        return contractTemp.build();
    }
}
