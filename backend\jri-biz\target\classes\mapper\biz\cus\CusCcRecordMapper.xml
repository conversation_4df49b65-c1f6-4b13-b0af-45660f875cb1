<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.cus.mapper.CusCcRecordMapper">


    <select id="listPage" resultType="com.jri.biz.cus.domain.vo.CusCcRecordListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.cus.domain.vo.CusCcRecordVO">

    </select>
    <select id="listByCcId" resultType="com.jri.biz.cus.domain.vo.CusCcRecordListVO">
        select *
        from cus_cc_record
        where cc_id = #{ccId} and biz_type = '0'
        order by create_time desc
    </select>
    <select id="businessListByCcId" resultType="com.jri.biz.cus.domain.vo.CusCcRecordListVO">
        select *
        from cus_cc_record
        where cc_id = #{ccId} and biz_type = '1'
        order by create_time desc
    </select>
</mapper>
