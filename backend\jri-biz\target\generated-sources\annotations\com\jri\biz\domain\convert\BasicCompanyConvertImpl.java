package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicCompany;
import com.jri.biz.domain.request.BasicCompanyForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BasicCompanyConvertImpl implements BasicCompanyConvert {

    @Override
    public BasicCompany convert(BasicCompanyForm form) {
        if ( form == null ) {
            return null;
        }

        BasicCompany.BasicCompanyBuilder basicCompany = BasicCompany.builder();

        basicCompany.id( form.getId() );
        basicCompany.name( form.getName() );
        basicCompany.sort( form.getSort() );
        basicCompany.remark( form.getRemark() );
        basicCompany.enable( form.getEnable() );
        basicCompany.address( form.getAddress() );
        basicCompany.contacts( form.getContacts() );
        basicCompany.phone( form.getPhone() );
        basicCompany.account( form.getAccount() );

        return basicCompany.build();
    }
}
