package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BusinessType;
import com.jri.biz.domain.request.BusinessTypeForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: java<PERSON>, environment: Java 17.0.16 (Microsoft)"
)
public class BusinessTypeConvertImpl implements BusinessTypeConvert {

    @Override
    public BusinessType convert(BusinessTypeForm form) {
        if ( form == null ) {
            return null;
        }

        BusinessType.BusinessTypeBuilder businessType = BusinessType.builder();

        businessType.id( form.getId() );
        businessType.typeName( form.getTypeName() );
        businessType.code( form.getCode() );
        businessType.contractType( form.getContractType() );
        businessType.sort( form.getSort() );
        businessType.enterpriseShowFlag( form.getEnterpriseShowFlag() );
        businessType.enable( form.getEnable() );

        return businessType.build();
    }
}
