package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BusinessProductActivity;
import com.jri.biz.domain.request.BusinessProductActivityForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:38+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class BusinessProductActivityConvertImpl implements BusinessProductActivityConvert {

    @Override
    public BusinessProductActivity convert(BusinessProductActivityForm form) {
        if ( form == null ) {
            return null;
        }

        BusinessProductActivity.BusinessProductActivityBuilder businessProductActivity = BusinessProductActivity.builder();

        return businessProductActivity.build();
    }
}
