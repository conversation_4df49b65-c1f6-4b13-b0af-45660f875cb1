<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.address.AddressSupplierMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.address.AddressSupplierListVO">
        select *
        from address_supplier
        <where>
            is_deleted = 0
            <if test="query.supplier != null and query.supplier != ''">
                and supplier like concat('%', #{query.supplier}, '%')
            </if>
            <if test="query.feeType != null and query.feeType != ''">
                and fee_type = #{query.feeType}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.address.AddressSupplierVO">
        select *
        from address_supplier where is_deleted = 0 and id = #{id}
    </select>
</mapper>
