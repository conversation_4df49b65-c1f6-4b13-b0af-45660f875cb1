package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSeaInventory;
import com.jri.biz.cus.domain.request.CusSeaInventoryForm;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T14:38:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.16 (Microsoft)"
)
public class CusSeaInventoryConvertImpl implements CusSeaInventoryConvert {

    @Override
    public CusSeaInventory convert(CusSeaInventoryForm form) {
        if ( form == null ) {
            return null;
        }

        CusSeaInventory.CusSeaInventoryBuilder cusSeaInventory = CusSeaInventory.builder();

        cusSeaInventory.id( form.getId() );
        cusSeaInventory.clueDuration( form.getClueDuration() );
        cusSeaInventory.clueSeaId( form.getClueSeaId() );
        cusSeaInventory.clueRecovery( form.getClueRecovery() );
        cusSeaInventory.clueStaffNum( form.getClueStaffNum() );
        cusSeaInventory.clueAdminNum( form.getClueAdminNum() );
        cusSeaInventory.clueStaffStatus( form.getClueStaffStatus() );
        cusSeaInventory.clueAdminStatus( form.getClueAdminStatus() );
        cusSeaInventory.cusDuration( form.getCusDuration() );
        cusSeaInventory.cusSeaId( form.getCusSeaId() );
        cusSeaInventory.cusRecovery( form.getCusRecovery() );
        cusSeaInventory.cusStaffNum( form.getCusStaffNum() );
        cusSeaInventory.cusAdminNum( form.getCusAdminNum() );
        cusSeaInventory.cusStaffStatus( form.getCusStaffStatus() );
        cusSeaInventory.cusAdminStatus( form.getCusAdminStatus() );

        return cusSeaInventory.build();
    }
}
